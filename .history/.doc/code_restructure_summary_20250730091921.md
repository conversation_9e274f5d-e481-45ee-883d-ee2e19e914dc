# 客户洞察助手代码结构调整总结

## 调整概述

根据前端业务需求，将客户洞察助手的配置功能整合到现有的智能体保存接口中，避免额外的接口调用。洞察配置查询已独立拆分，智能体详情接口不再返回洞察配置，保持接口职责单一。

## 主要调整内容

### 1. 智能体详情接口优化

**文件**: `admin/src/main/java/com/qc/agent/app/agent/service/impl/QcAiAgentServiceImpl.java`

**调整内容**:

- **移除**: 智能体详情接口不再返回洞察配置，保持接口职责单一
- 洞察配置查询已独立拆分到专门的接口

### 2. 智能体保存接口整合

**文件**: `admin/src/main/java/com/qc/agent/app/agent/model/dto/QcAiAgentSaveDTO.java`

**调整内容**:

- 添加了客户洞察维度配置字段: `insightDimensionConfigurations`
- 添加了客户洞察总结配置字段: `insightSummaryConfiguration`

**文件**: `admin/src/main/java/com/qc/agent/app/agent/service/impl/QcAiAgentServiceImpl.java`

**调整内容**:

- 在 `save` 方法中添加了客户洞察配置的保存逻辑
- **新增**: 只在 agent_id=8（客户洞察 agent）时才保存洞察配置
- 添加了 `saveCustomerInsightConfig` 私有方法
- 添加了异常处理，确保客户洞察配置保存失败不影响基本功能

### 3. 控制器调整

**文件**: `admin/src/main/java/com/qc/agent/app/agent/controller/CustomerInsightAssistantController.java`

**调整内容**:

- 保留了客户洞察意图识别接口 `/intent`（独立功能）
- 注释掉了配置相关接口:
  - `GET /{agentId}` - 获取工作区配置
  - `POST /saveWorkspace` - 保存工作区配置
- **新增**: 添加了独立的可用选项查询接口 `/available-options`
- 添加了 `@Deprecated` 注释说明功能已整合

### 4. 服务层调整

**文件**: `admin/src/main/java/com/qc/agent/app/agent/service/CustomerInsightService.java`

**调整内容**:

- **新增**: 添加了 `getAvailableOptions()` 方法，用于获取洞察可用选项

**文件**: `admin/src/main/java/com/qc/agent/app/agent/service/impl/CustomerInsightServiceImpl.java`

**调整内容**:

- **新增**: 实现了 `getAvailableOptions()` 方法，复用原有逻辑返回可用选项

## 接口变化

### 原有接口（已整合）

- `POST /ai-agent/assistant/customer-insight/saveWorkspace` → 整合到 `POST /ai-agent/agents/save`（仅 agent_id=8 时保存洞察配置）

### 独立接口

- `GET /ai-agent/assistant/customer-insight/{agentId}` → 保持独立，专门用于查询洞察配置

### 新增接口

- `POST /ai-agent/assistant/customer-insight/available-options` - 获取洞察可用选项（独立接口）

### 保留接口

- `POST /ai-agent/assistant/customer-insight/intent` - 客户洞察意图识别（独立功能）

## 前端调用方式

### 获取智能体详情

```javascript
// 获取智能体详情（不包含洞察配置）
GET /ai-agent/agents/{id}

// 返回数据
{
  // ... 原有字段
  // 不再包含洞察配置字段
}
```

### 获取洞察配置（独立接口）

```javascript
// 获取洞察配置
GET /ai-agent/assistant/customer-insight/{agentId}

// 返回数据
{
  "currentDimensionConfiguration": [...],
  "currentSummaryConfiguration": {...},
  "availableOptions": {...}
}
```

### 获取洞察可用选项

```javascript
// 新增独立接口
POST /ai-agent/assistant/customer-insight/available-options

// 返回数据
{
  "dimensions": [...]
}
```

### 保存智能体配置（仅 agent_id=8 时包含客户洞察配置）

```javascript
// 保存智能体配置
POST /ai-agent/agents/save

// 请求数据（仅agent_id=8时需要包含以下字段）
{
  // ... 原有字段
  "insightDimensionConfigurations": [...],
  "insightSummaryConfiguration": {...}
}
```

## 技术特点

1. **向后兼容**: 保持原有接口不变，新增字段为可选
2. **异常隔离**: 客户洞察配置的查询和保存失败不会影响智能体基本功能
3. **事务一致性**: 客户洞察配置保存在智能体保存事务中
4. **代码复用**: 复用现有的客户洞察服务逻辑
5. **精确控制**: 只在客户洞察 agent（agent_id=8）时才处理洞察相关配置
6. **接口分离**: 可用选项独立接口，便于前端灵活调用

## 注意事项

1. 前端需要更新调用方式，将客户洞察配置数据包含在智能体详情和保存请求中
2. **重要**: 洞察配置只在 agent_id=8 时返回和保存，其他智能体不会包含这些字段
3. 洞察可用选项需要单独调用 `/available-options` 接口获取
4. 客户洞察意图识别功能保持独立，不受此次调整影响
5. 原有的独立配置接口已注释，但保留代码以备需要时恢复

## 常量定义

```java
private static final Long CUSTOMER_INSIGHT_AGENT_ID = 8L;
```

使用常量替代硬编码的 agent_id，提高代码可维护性。
