-- =====================================================
-- 修正 customer_insight_schema.sql 中的提示词表定义
-- 替换原有的不完整定义
-- =====================================================

-- 删除原有的不完整表定义（如果存在）
DROP TABLE IF EXISTS qc_ai_prompt_fragment CASCADE;
DROP TABLE IF EXISTS qc_ai_main_prompt CASCADE;

-- 主提示词表（完整定义）
CREATE TABLE qc_ai_main_prompt (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    
    -- 提示词基本信息
    prompt_name VARCHAR(100) NOT NULL,           -- 提示词名称
    prompt_type VARCHAR(20) NOT NULL,           -- 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
    
    -- 关联信息
    config_id BIGINT -- 关联qc_ai_summary_config或者qc_ai_dimension_config的id
);
COMMENT ON TABLE qc_ai_main_prompt IS '主提示词表';
COMMENT ON COLUMN qc_ai_main_prompt.id IS '主提示词ID，主键';
COMMENT ON COLUMN qc_ai_main_prompt.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_main_prompt.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_main_prompt.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_main_prompt.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_main_prompt.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_main_prompt.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_main_prompt.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_name IS '提示词名称';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_type IS '提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE';
COMMENT ON COLUMN qc_ai_main_prompt.config_id IS '关联qc_ai_summary_config或者qc_ai_dimension_config的id';

-- 提示词片段表（完整定义）
CREATE TABLE qc_ai_prompt_fragment (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    
    -- 关联信息
    main_prompt_id BIGINT NOT NULL,             -- 关联主提示词ID
    
    -- 片段信息
    fragment_key VARCHAR(100) NOT NULL,         -- 片段键名
    fragment_value TEXT NOT NULL,               -- 片段值
    sort_order INTEGER DEFAULT 0                -- 排序号
);
COMMENT ON TABLE qc_ai_prompt_fragment IS '提示词片段表';
COMMENT ON COLUMN qc_ai_prompt_fragment.id IS '提示词片段ID，主键';
COMMENT ON COLUMN qc_ai_prompt_fragment.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_fragment.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_prompt_fragment.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_prompt_fragment.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_prompt_fragment.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_prompt_fragment.main_prompt_id IS '关联主提示词ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_key IS '片段键名';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_value IS '片段值';
COMMENT ON COLUMN qc_ai_prompt_fragment.sort_order IS '排序号';

-- 创建索引
CREATE INDEX idx_main_prompt_config_id ON qc_ai_main_prompt(config_id);
CREATE INDEX idx_main_prompt_type ON qc_ai_main_prompt(prompt_type);
CREATE INDEX idx_prompt_fragment_main_prompt_id ON qc_ai_prompt_fragment(main_prompt_id);
CREATE INDEX idx_prompt_fragment_sort_order ON qc_ai_prompt_fragment(sort_order);

-- 添加外键约束（可选）
-- ALTER TABLE qc_ai_prompt_fragment ADD CONSTRAINT fk_prompt_fragment_main_prompt 
--     FOREIGN KEY (main_prompt_id) REFERENCES qc_ai_main_prompt(id); 