-- 洞察提示词表
CREATE TABLE IF NOT EXISTS qc_ai_insight_prompt_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '提示词项ID',
    status VARCHAR(1) DEFAULT '1' COMMENT '状态 1:正常 0:已删除',
    agent_id BIGINT NOT NULL COMMENT '关联的智能体ID',
    config_id BIGINT NOT NULL COMMENT '关联的配置ID（维度配置ID或总结配置ID）',
    config_type VARCHAR(20) NOT NULL COMMENT '配置类型：DIMENSION-维度配置，SUMMARY-总结配置',
    prompt_key VARCHAR(100) NOT NULL COMMENT '提示词键名',
    prompt_value TEXT COMMENT '提示词内容',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    creator_id BIGINT COMMENT '创建人ID',
    creator_name VARCHAR(100) COMMENT '创建人姓名',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifyier_id BIGINT COMMENT '修改人ID',
    modifyier_name VARCHAR(100) COMMENT '修改人姓名',
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    INDEX idx_agent_config (agent_id, config_id, config_type),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洞察提示词表'; 