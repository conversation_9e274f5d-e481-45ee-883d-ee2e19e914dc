<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightPromptItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightPromptItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="agent_id" property="agentId" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="prompt_key" property="promptKey" jdbcType="VARCHAR"/>
        <result column="prompt_label" property="promptLabel" jdbcType="VARCHAR"/>
        <result column="prompt_value" property="promptValue" jdbcType="LONGVARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, agent_id, config_id, config_type, prompt_key, prompt_label, prompt_value, sort_order,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_insight_prompt_items
        WHERE id = #{id} AND status = '1'
    </select>

    <!-- 根据配置ID和类型查询 -->
    <select id="selectByConfigIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_insight_prompt_items
        WHERE config_id = #{configId} AND config_type = #{configType} AND status = '1'
        ORDER BY sort_order ASC
    </select>

    <!-- 根据智能体ID和配置类型查询 -->
    <select id="selectByAgentIdAndConfigType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_insight_prompt_items
        WHERE agent_id = #{agentId} AND config_type = #{configType} AND status = '1'
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightPromptItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_insight_prompt_items (
            status, agent_id, config_id, config_type, prompt_key, prompt_label, prompt_value, sort_order,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{agentId}, #{configId}, #{configType}, #{promptKey}, #{promptLabel}, #{promptValue}, #{sortOrder},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_insight_prompt_items (
            status, agent_id, config_id, config_type, prompt_key, prompt_label, prompt_value, sort_order,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.status}, #{item.agentId}, #{item.configId}, #{item.configType}, #{item.promptKey}, #{item.promptLabel}, #{item.promptValue}, #{item.sortOrder},
                #{item.creatorId}, #{item.creatorName}, #{item.createTime}, #{item.modifyierId}, #{item.modifyierName}, #{item.modifyTime}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightPromptItem">
        UPDATE qc_ai_insight_prompt_items
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="promptKey != null">prompt_key = #{promptKey},</if>
            <if test="promptLabel != null">prompt_label = #{promptLabel},</if>
            <if test="promptValue != null">prompt_value = #{promptValue},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据配置ID和类型删除 -->
    <update id="deleteByConfigIdAndType">
        UPDATE qc_ai_insight_prompt_items
        SET status = '0'
        WHERE config_id = #{configId} AND config_type = #{configType}
    </update>

    <!-- 根据智能体ID删除 -->
    <update id="deleteByAgentId" parameterType="java.lang.Long">
        UPDATE qc_ai_insight_prompt_items
        SET status = '0'
        WHERE agent_id = #{agentId}
    </update>

</mapper> 