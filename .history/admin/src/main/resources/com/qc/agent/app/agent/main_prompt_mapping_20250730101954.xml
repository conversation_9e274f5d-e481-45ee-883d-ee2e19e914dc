<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.MainPromptMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.MainPrompt">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="prompt_name" property="promptName" jdbcType="VARCHAR"/>
        <result column="prompt_type" property="promptType" jdbcType="VARCHAR"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time,
        prompt_name, prompt_type, config_id
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_main_prompt
        WHERE id = #{id}
    </select>

    <!-- 根据配置ID和类型查询 -->
    <select id="selectByConfigIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_main_prompt
        WHERE config_id = #{configId} AND prompt_type = #{promptType}
    </select>

    <!-- 根据配置ID查询所有提示词 -->
    <select id="selectByConfigId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_main_prompt
        WHERE config_id = #{configId}
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.MainPrompt" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_main_prompt (
            status, prompt_name, prompt_type, config_id,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{promptName}, #{promptType}, #{configId},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.MainPrompt">
        UPDATE qc_ai_main_prompt
        <set>
            <if test="promptName != null">prompt_name = #{promptName},</if>
            <if test="promptType != null">prompt_type = #{promptType},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_main_prompt WHERE id = #{id}
    </delete>

    <!-- 根据配置ID和类型删除 -->
    <delete id="deleteByConfigIdAndType">
        DELETE FROM qc_ai_main_prompt 
        WHERE config_id = #{configId} AND prompt_type = #{promptType}
    </delete>

</mapper> 