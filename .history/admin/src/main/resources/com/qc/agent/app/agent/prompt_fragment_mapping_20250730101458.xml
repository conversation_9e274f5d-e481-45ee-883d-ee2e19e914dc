<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.PromptFragmentMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.PromptFragment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="main_prompt_id" property="mainPromptId" jdbcType="BIGINT"/>
        <result column="fragment_key" property="fragmentKey" jdbcType="VARCHAR"/>
        <result column="fragment_value" property="fragmentValue" jdbcType="TEXT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time,
        main_prompt_id, fragment_key, fragment_value, sort_order
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE id = #{id}
    </select>

    <!-- 根据主提示词ID查询 -->
    <select id="selectByMainPromptId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE main_prompt_id = #{mainPromptId}
    </select>

    <!-- 根据主提示词ID查询并按排序号排序 -->
    <select id="selectByMainPromptIdOrderBySort" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE main_prompt_id = #{mainPromptId}
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.PromptFragment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_prompt_fragment (
            status, main_prompt_id, fragment_key, fragment_value, sort_order,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{mainPromptId}, #{fragmentKey}, #{fragmentValue}, #{sortOrder},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_prompt_fragment (
            status, main_prompt_id, fragment_key, fragment_value, sort_order,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES
        <foreach collection="fragments" item="fragment" separator=",">
            (
                #{fragment.status}, #{fragment.mainPromptId}, #{fragment.fragmentKey}, #{fragment.fragmentValue}, #{fragment.sortOrder},
                #{fragment.creatorId}, #{fragment.creatorName}, #{fragment.createTime}, #{fragment.modifyierId}, #{fragment.modifyierName}, #{fragment.modifyTime}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.PromptFragment">
        UPDATE qc_ai_prompt_fragment
        <set>
            <if test="mainPromptId != null">main_prompt_id = #{mainPromptId},</if>
            <if test="fragmentKey != null">fragment_key = #{fragmentKey},</if>
            <if test="fragmentValue != null">fragment_value = #{fragmentValue},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_prompt_fragment WHERE id = #{id}
    </delete>

    <!-- 根据主提示词ID删除 -->
    <delete id="deleteByMainPromptId" parameterType="java.lang.Long">
        DELETE FROM qc_ai_prompt_fragment WHERE main_prompt_id = #{mainPromptId}
    </delete>

</mapper> 