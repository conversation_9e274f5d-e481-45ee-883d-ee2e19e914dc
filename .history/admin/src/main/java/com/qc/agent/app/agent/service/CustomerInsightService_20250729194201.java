package com.qc.agent.app.agent.service;

import com.qc.agent.app.agent.model.dto.InsightConfigWorkspaceDTO;
import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.model.vo.InsightConfigWorkspaceVO;

import java.util.Map;

/**
 * 客户洞察服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public interface CustomerInsightService {
    
    /**
     * 客户洞察意图识别
     *
     * @param query 查询参数
     * @return 意图识别结果
     */
    Map<String, Object> intent(QcAiCustomerInsightAssistantQuery query);

    /**
     * 获取指定Agent的完整配置工作区
     * (包括当前配置和所有可用选项)
     *
     * @param agentId 智能体ID
     * @return 配置工作区视图对象
     */
    InsightConfigWorkspaceVO getWorkspace(Long agentId);

    /**
     * 全量保存指定Agent的配置
     *
     * @param workspaceDTO 包含agentId和完整配置列表的数据
     * @return 是否成功
     */
    boolean saveWorkspace(InsightConfigWorkspaceDTO workspaceDTO);

} 