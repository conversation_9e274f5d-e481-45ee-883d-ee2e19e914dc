package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主提示词表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MainPrompt extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主提示词ID，主键
     */
    private Long id;

    /**
     * 提示词名称
     */
    private String promptName;

    /**
     * 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
     */
    private String promptType;

    /**
     * 关联qc_ai_summary_config或者qc_ai_dimension_config的id
     */
    private Long configId;
} 