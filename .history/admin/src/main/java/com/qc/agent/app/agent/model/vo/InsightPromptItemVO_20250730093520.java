package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;

import lombok.Data;

/**
 * 洞察提示词项视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightPromptItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提示词项ID
     */
    private Long id;

    /**
     * 关联的智能体ID
     */
    private Long agentId;

    /**
     * 关联的配置ID（维度配置ID或总结配置ID）
     */
    private Long configId;

    /**
     * 配置类型：DIMENSION-维度配置，SUMMARY-总结配置
     */
    private String configType;

    /**
     * 提示词键名
     */
    private String promptKey;

    /**
     * 提示词标签
     */
    private String promptLabel;

    /**
     * 提示词内容
     */
    private String promptValue;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}