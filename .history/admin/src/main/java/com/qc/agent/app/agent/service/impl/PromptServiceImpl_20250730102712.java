package com.qc.agent.app.agent.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.qc.agent.app.agent.mapper.MainPromptMapper;
import com.qc.agent.app.agent.mapper.PromptFragmentMapper;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.MainPrompt;
import com.qc.agent.app.agent.model.entity.PromptFragment;
import com.qc.agent.app.agent.service.PromptService;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.register.UserManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 提示词服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PromptServiceImpl implements PromptService {

    @Resource
    private MainPromptMapper mainPromptMapper;

    @Resource
    private PromptFragmentMapper promptFragmentMapper;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MainPromptDTO getMainPromptByConfigIdAndType(Long configId, String promptType) {
        MainPrompt mainPrompt = mainPromptMapper.selectByConfigIdAndType(configId, promptType);
        if (mainPrompt == null) {
            return null;
        }

        MainPromptDTO dto = new MainPromptDTO();
        BeanUtils.copyProperties(mainPrompt, dto);

        // 获取片段列表
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPrompt.getId());
        List<PromptFragmentDTO> fragmentDtos = new ArrayList<>();
        for (PromptFragment fragment : fragments) {
            PromptFragmentDTO fragmentDto = new PromptFragmentDTO();
            BeanUtils.copyProperties(fragment, fragmentDto);
            fragmentDtos.add(fragmentDto);
        }
        dto.setFragments(fragmentDtos);

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMainPrompt(MainPromptDTO mainPromptDTO) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        // 先查询是否已存在相同配置ID和类型的主提示词
        MainPrompt existingMainPrompt = mainPromptMapper.selectByConfigIdAndType(mainPromptDTO.getConfigId(), mainPromptDTO.getPromptType());

        MainPrompt mainPrompt = new MainPrompt();
        BeanUtils.copyProperties(mainPromptDTO, mainPrompt);

        if (existingMainPrompt == null) {
            // 新增
            mainPrompt.setStatus("1");
            mainPrompt.setCreatorId(currentUserId);
            mainPrompt.setCreatorName(currentUserName);
            mainPrompt.setCreateTime(now);
            mainPrompt.setModifyierId(currentUserId);
            mainPrompt.setModifyierName(currentUserName);
            mainPrompt.setModifyTime(now);
            mainPromptMapper.insert(mainPrompt);
        } else {
            // 更新
            mainPrompt.setId(existingMainPrompt.getId());
            mainPrompt.setModifyierId(currentUserId);
            mainPrompt.setModifyierName(currentUserName);
            mainPrompt.setModifyTime(now);
            mainPromptMapper.updateById(mainPrompt);
        }

        // 保存片段
        if (!CollectionUtils.isEmpty(mainPromptDTO.getFragments())) {
            // 先删除现有片段
            promptFragmentMapper.deleteByMainPromptId(mainPrompt.getId());

            // 保存新片段
            List<PromptFragment> fragments = new ArrayList<>();
            for (int i = 0; i < mainPromptDTO.getFragments().size(); i++) {
                PromptFragmentDTO fragmentDto = mainPromptDTO.getFragments().get(i);
                PromptFragment fragment = new PromptFragment();
                BeanUtils.copyProperties(fragmentDto, fragment);
                fragment.setMainPromptId(mainPrompt.getId());
                fragment.setSortOrder(fragmentDto.getSortOrder() != null ? fragmentDto.getSortOrder() : i + 1);
                fragment.setStatus("1");
                fragment.setCreatorId(currentUserId);
                fragment.setCreatorName(currentUserName);
                fragment.setCreateTime(now);
                fragment.setModifyierId(currentUserId);
                fragment.setModifyierName(currentUserName);
                fragment.setModifyTime(now);
                fragments.add(fragment);
            }
            promptFragmentMapper.batchInsert(fragments);
        }

        return true;
    }

    @Override
    public List<PromptFragmentDTO> getFragmentsByMainPromptId(Long mainPromptId) {
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPromptId);
        List<PromptFragmentDTO> fragmentDtos = new ArrayList<>();
        for (PromptFragment fragment : fragments) {
            PromptFragmentDTO fragmentDto = new PromptFragmentDTO();
            BeanUtils.copyProperties(fragment, fragmentDto);
            fragmentDtos.add(fragmentDto);
        }
        return fragmentDtos;
    }

    @Override
    public JsonNode fragmentsToJsonObject(List<PromptFragmentDTO> fragments) {
        ObjectNode rootNode = objectMapper.createObjectNode();

        for (PromptFragmentDTO fragment : fragments) {
            setNestedValue(rootNode, fragment.getFragmentKey(), fragment.getFragmentValue());
        }

        return rootNode;
    }

    @Override
    public List<PromptFragmentDTO> jsonObjectToFragments(JsonNode jsonNode, Long mainPromptId) {
        List<PromptFragmentDTO> fragments = new ArrayList<>();
        int sortOrder = 1;

        Iterator<Entry<String, JsonNode>> fields = jsonNode.fields();
        while (fields.hasNext()) {
            Entry<String, JsonNode> field = fields.next();
            fragments.addAll(extractFragments(field.getKey(), field.getValue(), mainPromptId, sortOrder++));
        }

        return fragments;
    }

    @Override
    public String generatePromptFromFragments(List<PromptFragmentDTO> fragments, Map<String, Object> variables) {
        if (CollectionUtils.isEmpty(fragments)) {
            return "";
        }

        StringBuilder prompt = new StringBuilder();
        for (PromptFragmentDTO fragment : fragments) {
            String value = fragment.getFragmentValue();
            if (variables != null) {
                value = replaceVariables(value, variables);
            }
            prompt.append(value).append("\n");
        }

        return prompt.toString().trim();
    }

    /**
     * 设置嵌套值
     */
    private void setNestedValue(ObjectNode node, String key, String value) {
        String[] parts = key.split("\\.");
        ObjectNode current = node;

        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            if (!current.has(part)) {
                current.set(part, objectMapper.createObjectNode());
            }
            current = (ObjectNode) current.get(part);
        }

        // 尝试解析为JSON数组，否则作为字符串
        try {
            JsonNode jsonValue = objectMapper.readTree(value);
            current.set(parts[parts.length - 1], jsonValue);
        } catch (Exception e) {
            current.put(parts[parts.length - 1], value);
        }
    }

    /**
     * 提取片段
     */
    private List<PromptFragmentDTO> extractFragments(String key, JsonNode value, Long mainPromptId, int sortOrder) {
        List<PromptFragmentDTO> fragments = new ArrayList<>();

        if (value.isObject()) {
            Iterator<Entry<String, JsonNode>> fields = value.fields();
            while (fields.hasNext()) {
                Entry<String, JsonNode> field = fields.next();
                String newKey = key + "." + field.getKey();
                fragments.addAll(extractFragments(newKey, field.getValue(), mainPromptId, sortOrder++));
            }
        } else {
            PromptFragmentDTO fragment = new PromptFragmentDTO();
            fragment.setMainPromptId(mainPromptId);
            fragment.setFragmentKey(key);
            fragment.setFragmentValue(value.asText());
            fragment.setSortOrder(sortOrder);
            fragments.add(fragment);
        }

        return fragments;
    }

    /**
     * 替换变量
     */
    private String replaceVariables(String text, Map<String, Object> variables) {
        if (variables == null || variables.isEmpty()) {
            return text;
        }

        String result = text;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }

        return result;
    }
}