package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提示词片段表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PromptFragment extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提示词片段ID，主键
     */
    private Long id;

    /**
     * 关联主提示词ID
     */
    private Long mainPromptId;

    /**
     * 片段键名
     */
    private String fragmentKey;

    /**
     * 片段值
     */
    private String fragmentValue;

    /**
     * 排序号
     */
    private Integer sortOrder;
}