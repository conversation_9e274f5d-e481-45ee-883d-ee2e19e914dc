package com.qc.agent.app.agent.service.impl;

import static com.qc.agent.app.file_process.service.impl.DoubaoFileContextCachingServiceImpl.REDIS_KEY_TEMPLATE;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.qc.agent.app.agent.mapper.QcAiAgentAuthorityDistributeDetailMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentKnowledgeMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.model.dto.InsightConfigWorkspaceDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentDeptDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentExtConfig;
import com.qc.agent.app.agent.model.dto.QcAiAgentPublishDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentSaveDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentUserDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail;
import com.qc.agent.app.agent.model.po.QcAiAgentKnowledge;
import com.qc.agent.app.agent.model.query.QcAiAgentBaseQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentChatQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentQuery;
import com.qc.agent.app.agent.model.vo.AgentsClientQueryVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentDetailVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentVO;
import com.qc.agent.app.agent.service.CustomerInsightService;
import com.qc.agent.app.agent.service.QcAiAgentService;
import com.qc.agent.app.agent.util.ContextUtil;
import com.qc.agent.app.agent.util.DataBaseMapperUtils;
import com.qc.agent.app.file_process.service.impl.KimiFileContextCachingService;
import com.qc.agent.app.knowledge.mapper.KnowledgeInfoMapper;
import com.qc.agent.app.knowledge.model.QcKnowledgeInfo;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentFileDetailMapper;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentKnowledgeCategoryMappingMapper;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentKnowledgeDetailMapper;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentRecognitionMapper;
import com.qc.agent.app.workflow.inner.mapper.QcUploadFileMapper;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentFileDetail;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeCategoryMapping;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeDetail;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated;
import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.platform.config.FileProperty;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.HttpUtil;
import com.qc.agent.platform.util.UUIDUtils;
import com.qc.agent.redis.RedisClient;
import com.qc.agent.utils.QcAiBusinessRedisKeyConstants;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class QcAiAgentServiceImpl implements QcAiAgentService {

    @Resource
    private QcAiAgentMapper qcAiAgentMapper;

    @Resource
    private QcAiAgentKnowledgeMapper qcAiAgentKnowledgeMapper;

    @Resource
    private KnowledgeInfoMapper knowledgeInfoMapper;
    @Resource
    private QcAiAgentAuthorityDistributeDetailMapper qcAiAgentAuthorityDistributeDetailMapper;

    @Resource
    private QcAiAgentIntentRecognitionMapper qcAiAgentIntentRecognitionMapper;
    @Resource
    private QcAiAgentIntentKnowledgeDetailMapper qcAiAgentIntentKnowledgeDetailMapper;
    @Resource
    private QcAiAgentIntentKnowledgeCategoryMappingMapper qcAiAgentIntentKnowledgeCategoryMappingMapper;
    @Resource
    private QcAiAgentIntentFileDetailMapper qcAiAgentIntentFileDetailMapper;
    @Resource
    private QcUploadFileMapper qcUploadFileMapper;

    @Resource
    private RedisClient redisClient;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    @Value("${file.dns.url}")
    private String url;

    @Autowired
    private FileProperty fileProperty;

    @Resource
    private KimiFileContextCachingService kimiFileContextCachingService;

    private final ExecutorService chatThreadPool = Executors.newFixedThreadPool(20);

    @Resource
    private QcAiAgentConversationServiceImpl QcAiAgentConversationServiceImpl;

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource
    private DatasourceService datasourceService;
    @Resource
    private LeadingQuestionService leadingQuestionService;

    @Resource
    private CustomerInsightService customerInsightService;

    private static final String ADD_AGENT_LOCK_PREFIX = "ADD_AGENT_LOCK_";

    private static final Long PRODUCT_TALK_AGENT_ID = 6L;
    private static final Long CUSTOMER_INSIGHT_AGENT_ID = 8L;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QcAiAgent save(QcAiAgentSaveDTO agent) {
        QcAiAgent aiAgent;
        if (agent.getId() != null) {
            aiAgent = updateAgent(agent);
        } else {
            aiAgent = addAiAgent(agent);
        }

        // 只有客户洞察agent（agent_id=8）才保存洞察配置
        if (aiAgent.getId() != null && aiAgent.getId().equals(CUSTOMER_INSIGHT_AGENT_ID)) {
            saveCustomerInsightConfig(agent, aiAgent.getId());
        }

        if (agent.savePublish()) {
            QcAiAgentPublishDTO publishDTO = new QcAiAgentPublishDTO();
            publishDTO.setId(aiAgent.getId());
            publishDTO.setUserId(agent.getUserId());
            publishDTO.setUserName(agent.getUserName());
            publishDTO.setDeptList(agent.getDeptList());
            publishDTO.setUserList(agent.getUserList());
            publishDTO.setShowChatLogContentType(agent.getShowChatLogContentType());
            publish(publishDTO);
        }
        return aiAgent;
    }

    private QcAiAgent updateAgent(QcAiAgentSaveDTO agent) {
        QcAiAgent qcAiAgent = initUpdateAgent(agent);
        saveExtConfig(agent.getExtConfigs(), qcAiAgent.getId());
        if (agent.baseSave()) {
            qcAiAgentMapper.updateBaseInfo(qcAiAgent);
        } else {
            saveIntentList(agent.getIntentList(), qcAiAgent, "1");
            qcAiAgentMapper.update(qcAiAgent);
            qcAiAgentKnowledgeMapper.deleteByAgentId(qcAiAgent.getId());
            // 未开启意图识别才更新知识库
            if (!Objects.equals(agent.getIntentIsEnabled(), "1")
                    && CollectionUtils.isNotEmpty(agent.getKnowledgeIds())) {
                List<QcAiAgentKnowledge> qcAiAgentKnowledge = initQcAiAgentKnowledge(agent.getKnowledgeIds(),
                        qcAiAgent.getId());
                qcAiAgentKnowledgeMapper.batchInsert(qcAiAgentKnowledge);
            }
        }
        return qcAiAgent;
    }

    private void saveExtConfig(Map<String, String> extConfigMap, Long agentId) {
        if (extConfigMap == null || extConfigMap.isEmpty() || agentId == null) {
            return;
        }
        extConfigMap.forEach((key, value) -> {
            qcAiAgentMapper.updateExtConfigValue(
                    agentId,
                    key,
                    value,
                    LocalDateTime.now());
        });
    }

    private QcAiAgent initUpdateAgent(QcAiAgentSaveDTO agent) {
        return QcAiAgent.builder()
                .id(agent.getId())
                .modifyierId(agent.getUserId())
                .modifyierName(agent.getUserName())
                .modifyTime(LocalDateTime.now())
                .modelId(agent.getModelId())
                .name(agent.getName())
                .logo(agent.getLogo())
                .prompt(agent.getPrompt())
                .leadingQuestion(agent.getLeadingQuestion())
                .description(agent.getDescription())
                .contextSearchAmount(agent.getContextSearchAmount())
                .introduction(agent.getIntroduction())
                .internetSearch(agent.getInternetSearch())
                .showChatLogContentType(agent.getShowChatLogContentType())
                .nullResultAnswer(agent.getNullResultAnswer())
                .errorResultAnswer(agent.getErrorResultAnswer())
                .modelMaxTokens(agent.getModelMaxTokens())
                .modelTopP(agent.getModelTopP())
                .modelTemperature(agent.getModelTemperature())
                .maxRecallCount(agent.getMaxRecallCount())
                .minMatchThreshold(agent.getMinMatchThreshold())
                .qaMinMatchThreshold(agent.getQaMinMatchThreshold())
                .searchScope(agent.getSearchScope())
                .splitSqlPrompt(agent.getSplitSqlPrompt())
                .bizPrompt(agent.getBizPrompt())
                .intentIsEnabled(agent.getIntentIsEnabled())
                .build();
    }

    private QcAiAgent addAiAgent(QcAiAgentSaveDTO agent) {
        String lockKey = ADD_AGENT_LOCK_PREFIX + agent.getName() + UserManager.getTenantUser().getUserName();

        Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "lock", 10, TimeUnit.SECONDS);
        if (lock == null || !lock) {
            throw new BizException("操作频繁，请勿重复操作");
        }

        try {
            QcAiAgent qcAiAgent = initQcAiAgent(agent);
            saveIntentList(agent.getIntentList(), qcAiAgent, "1");
            qcAiAgentMapper.insert(qcAiAgent);
            // 未开启意图识别才更新知识库
            if (!Objects.equals(agent.getIntentIsEnabled(), "1")
                    && CollectionUtils.isNotEmpty(agent.getKnowledgeIds())) {
                List<QcAiAgentKnowledge> qcAiAgentKnowledge = initQcAiAgentKnowledge(agent.getKnowledgeIds(),
                        qcAiAgent.getId());
                qcAiAgentKnowledgeMapper.batchInsert(qcAiAgentKnowledge);
            }
            return qcAiAgent;
        } catch (Exception e) {
            throw new BizException("操作失败，请稍后重试");
        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    /**
     * 保存意图识别相关
     *
     * @param intentList
     * @param agent
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveIntentList(List<QcAiAgentIntentRecognition> intentList, QcAiAgent agent, String status) {
        if (CollectionUtils.isEmpty(intentList) || Objects.equals(agent.getIntentIsEnabled(), "0")) {
            log.info(
                    "Intent list is empty or intentIsEnabled is disable for agent ID: {},intentIsEnabled: {}. Skipping intent processing.",
                    agent.getId(),
                    agent.getIntentIsEnabled());
            return;
        }

        log.info("Processing {} intents for agent ID: {}", intentList.size(), agent.getId());
        final TenantUser currentUser = UserManager.getTenantUser(); // Get once
        final Long agentId = agent.getId();
        final LocalDateTime now = LocalDateTime.now();

        List<QcAiAgentIntentKnowledgeDetail> detailList = new ArrayList<>();
        List<QcAiAgentIntentKnowledgeCategoryMapping> mappingList = new ArrayList<>();
        List<QcUploadFile> fileList = new ArrayList<>();
        List<QcAiAgentIntentFileDetail> fileDetailList = new ArrayList<>();

        int intentCodeCounter = 0;
        for (QcAiAgentIntentRecognition intent : intentList) {
            if (intent.getId() == null) {
                intent.setId(UUIDUtils.getUUID2Long());
            }
            intent.setStatus(status);
            intent.setCreatorId(currentUser.getUserId());
            intent.setCreatorName(currentUser.getUserName());
            intent.setCreateTime(now);
            String intentCode = String.valueOf(++intentCodeCounter);
            if (Objects.equals(intent.getIntentName(), "其他")) {
                intentCode = "OTHER";
            }
            intent.setIntentCode(intentCode);
            intent.setAgentId(agentId);
            // 关联知识库
            if (CollectionUtils.isNotEmpty(intent.getRelateKnowledgeList())) {
                for (QcAiKnowledgeRelated knowledgeRelated : intent.getRelateKnowledgeList()) {
                    QcAiAgentIntentKnowledgeDetail detail = new QcAiAgentIntentKnowledgeDetail();
                    detail.setId(UUIDUtils.getUUID2Long());
                    detail.setStatus(status);
                    detail.setCreatorId(currentUser.getUserId());
                    detail.setCreatorName(currentUser.getUserName());
                    detail.setCreateTime(now);
                    detail.setIntentId(intent.getId());
                    detail.setAgentId(agentId);
                    detail.setCollectionId(knowledgeRelated.getCollectionId());
                    detailList.add(detail);

                    if (!CollectionUtils.isEmpty(knowledgeRelated.getRelateDocList())) {
                        for (QcAiKnowledgeFile file : knowledgeRelated.getRelateDocList()) {
                            QcAiAgentIntentKnowledgeCategoryMapping mapping = new QcAiAgentIntentKnowledgeCategoryMapping();
                            mapping.setId(UUIDUtils.getUUID2Long());
                            mapping.setStatus(status);
                            mapping.setAgentId(agentId);
                            mapping.setCollectionId(file.getCollectionId()); // Should this be
                                                                             // knowledgeRelated.getCollectionId()?
                            mapping.setCategoryId(file.getCategoryId());
                            mapping.setRelationId(detail.getId()); // Links to the detail record
                            mappingList.add(mapping);
                        }
                    }
                }
            }
            // 关联文件
            if (CollectionUtils.isNotEmpty(intent.getRelateFileList())) {
                for (QcUploadFile file : intent.getRelateFileList()) {
                    file.setId(UUIDUtils.getUUID2Long());
                    file.setStatus(status);
                    file.setCreateUserId(currentUser.getUserId());
                    file.setCreateUserName(currentUser.getUserName());
                    file.setCreateTime(now);
                    file.setIntentId(intent.getId());
                    fileList.add(file);

                    QcAiAgentIntentFileDetail detail = new QcAiAgentIntentFileDetail();
                    detail.setId(UUIDUtils.getUUID2Long());
                    detail.setStatus(status);
                    detail.setCreatorId(currentUser.getUserId());
                    detail.setCreatorName(currentUser.getUserName());
                    detail.setCreateTime(now);
                    detail.setAgentId(agentId);
                    detail.setIntentId(intent.getId());
                    detail.setFileId(file.getId());
                    fileDetailList.add(detail);
                }
            }
        }

        log.debug("Deleting existing intent related data for agent ID: {},status: {}", agentId, status);
        qcAiAgentIntentRecognitionMapper.deleteByAgentId(agentId, status);
        qcAiAgentIntentKnowledgeDetailMapper.deleteByAgentId(agentId, status);
        qcAiAgentIntentKnowledgeCategoryMappingMapper.deleteByAgentId(agentId, status);
        qcUploadFileMapper.deleteByAgentId(agentId, status);
        qcAiAgentIntentFileDetailMapper.deleteByAgentId(agentId, status);

        log.debug("Batch inserting {} intents, {} details, {} mappings, {} files for agent ID: {}",
                intentList.size(), detailList.size(), mappingList.size(), fileList.size(), agentId);
        DataBaseMapperUtils.batchInsert(intentList, qcAiAgentIntentRecognitionMapper::batchInsert);
        DataBaseMapperUtils.batchInsert(detailList, qcAiAgentIntentKnowledgeDetailMapper::batchInsert);
        DataBaseMapperUtils.batchInsert(mappingList, qcAiAgentIntentKnowledgeCategoryMappingMapper::batchInsert);
        DataBaseMapperUtils.batchInsert(fileList, qcUploadFileMapper::batchInsert);
        DataBaseMapperUtils.batchInsert(fileDetailList, qcAiAgentIntentFileDetailMapper::batchInsert);
        parseFileCacheOss(fileList, agent.getId());
    }

    private void parseFileCacheOss(List<QcUploadFile> fileList, Long agentId) {
        Long tenantId = UserManager.getTenantUser().getTenantId();
        String logPrefix = String.format("[tenantId=%s, agentId=%s]", tenantId, agentId);
        if (!fileList.isEmpty()) {
            chatThreadPool.submit(ContextUtil.wrapWithContext(() -> {
                for (QcUploadFile file : fileList) {
                    // 删除缓存
                    final var doubaoRedisKey = String.format(REDIS_KEY_TEMPLATE, file.getIntentId());
                    redisClient.delete(doubaoRedisKey);
                    final var kimiRedisKey = QcAiBusinessRedisKeyConstants.MODEL_CACHE_KIMI + "cache-messages:"
                            + file.getIntentId();
                    redisClient.delete(kimiRedisKey);
                    String uri = file.getBasePath() + file.getPath() + file.getName();
                    String parsedOssPath = kimiFileContextCachingService.generateParsedFilePath(tenantId,
                            file.getIntentId(), uri);
                    log.info("{} 原始文件路径: {}, 预期解析路径: {}", logPrefix, uri, parsedOssPath);
                    try {
                        kimiFileContextCachingService.reparseUploadFile(logPrefix, file, parsedOssPath);
                    } catch (IOException e) {
                        log.error("{} 文件解析失败: {}", logPrefix, e.getMessage(), e);
                    }
                }
            }));
        }
    }

    /**
     * agent授权
     *
     * @param publishDTO
     */
    public void authorityDetail(QcAiAgentPublishDTO publishDTO) {
        qcAiAgentAuthorityDistributeDetailMapper.deleteByAgentId(publishDTO.getId());

        QcAiAgent qcAiAgent = QcAiAgent.builder()
                .id(publishDTO.getId())
                .modifyierId(UserManager.getTenantUser().getUserId())
                .modifyierName(UserManager.getTenantUser().getUserName())
                .modifyTime(LocalDateTime.now())
                .showChatLogContentType(publishDTO.getShowChatLogContentType())
                .build();
        qcAiAgentMapper.update(qcAiAgent);

        List<QcAiAgentAuthorityDistributeDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(publishDTO.getUserList())) {
            for (QcAiAgentUserDTO userDTO : publishDTO.getUserList()) {
                QcAiAgentAuthorityDistributeDetail record = QcAiAgentAuthorityDistributeDetail.builder()
                        .id(UUIDUtils.getUUID2Long())
                        .status("1")
                        .creatorId(UserManager.getTenantUser().getUserId())
                        .createTime(LocalDateTime.now())
                        .agentId(publishDTO.getId())
                        .userId(userDTO.getId())
                        .userName(userDTO.getName())
                        .build();
                list.add(record);
            }
        }
        if (CollectionUtils.isNotEmpty(publishDTO.getDeptList())) {
            for (QcAiAgentDeptDTO deptDTO : publishDTO.getDeptList()) {
                QcAiAgentAuthorityDistributeDetail record = QcAiAgentAuthorityDistributeDetail.builder()
                        .id(UUIDUtils.getUUID2Long())
                        .status("1")
                        .creatorId(UserManager.getTenantUser().getUserId())
                        .createTime(LocalDateTime.now())
                        .agentId(publishDTO.getId())
                        .deptId(deptDTO.getId())
                        .deptName(deptDTO.getName())
                        .build();
                list.add(record);
            }
        }
        DataBaseMapperUtils.batchInsert(list, qcAiAgentAuthorityDistributeDetailMapper::batchInsert);
    }

    private List<QcAiAgentKnowledge> initQcAiAgentKnowledge(List<Long> knowledgeIds, Long id) {
        return knowledgeIds.stream()
                .map(item -> QcAiAgentKnowledge.builder()
                        .id(UUIDUtils.getUUID2Long())
                        .status("1")
                        .creatorId(UserManager.getTenantUser().getUserId())
                        .createTime(LocalDateTime.now())
                        .creatorName(UserManager.getTenantUser().getUserName())
                        .knowledgeId(item)
                        .agentId(id)
                        .build())
                .collect(Collectors.toList());
    }

    private QcAiAgent initQcAiAgent(QcAiAgentSaveDTO agent) {
        QcAiAgentChatQuery query = new QcAiAgentChatQuery();
        query.setDesc(agent.getName() + "," + agent.getDescription());
        // String leadingQuestion =
        // QcAiAgentConversationServiceImpl.generateDefaultQuestion(query);
        return QcAiAgent.builder()
                .id(UUIDUtils.getUUID2Long())
                .status("1")
                .createTime(LocalDateTime.now())
                .creatorId(agent.getUserId())
                .creatorName(agent.getUserName())
                .name(agent.getName())
                .showChatLogContentType(agent.getShowChatLogContentType())
                .logo(agent.getLogo())
                .prompt(agent.getPrompt())
                .internetSearch(agent.getInternetSearch())
                .internalFlag(QcAiAgent.CUSTOM_AGENT)
                .contextSearchAmount(agent.getContextSearchAmount())
                .description(agent.getDescription())
                .introduction("您好，我是" + agent.getName() + "，擅长解答各种问题，快来试试吧！")
                // .leadingQuestion(leadingQuestion)
                .nullResultAnswer("抱歉，我无法回答该问题的准确信息。如果您有任何其他问题需要帮助，请随时告诉我。")
                .errorResultAnswer("非常感谢您的提问！目前由于咨询量较大，Agent正在全力以赴地处理每一个问题。请稍候片刻，感谢您的耐心等待！")
                .modelId(agent.getModelId())
                .publishFlag(QcAiAgent.PENDING_PUBLISH)
                .modelMaxTokens(agent.getModelMaxTokens())
                .modelTopP(agent.getModelTopP())
                .modelTemperature(agent.getModelTemperature())
                .maxRecallCount(agent.getMaxRecallCount())
                .minMatchThreshold(agent.getMinMatchThreshold())
                .qaMinMatchThreshold(agent.getQaMinMatchThreshold())
                .searchScope(agent.getSearchScope())
                .splitSqlPrompt(agent.getSplitSqlPrompt())
                .intentIsEnabled(agent.getIntentIsEnabled())
                .build();
    }

    @Override
    public List<QcAiAgentVO> queryCustomAgents(String name, String publishFlag) {
        List<QcAiAgentVO> qcAiAgentVOS = qcAiAgentMapper.queryCustomAgents(UserManager.getTenantUser().getUserId(),
                name, publishFlag, checkCanViewAll());
        if (CollectionUtils.isNotEmpty(qcAiAgentVOS)) {
            for (QcAiAgentVO qcAiAgentVO : qcAiAgentVOS) {
                qcAiAgentVO.setAgentLogoDomain(url);
            }
        }
        return qcAiAgentVOS;
    }

    @Override
    public List<QcAiAgentVO> seekAgents(QcAiAgentQuery query) {
        query.setUserId(UserManager.getTenantUser().getUserId());
        List<QcAiAgentVO> qcAiAgentVOS = qcAiAgentMapper.seekAgents(query);
        if (CollectionUtils.isNotEmpty(qcAiAgentVOS)) {
            for (QcAiAgentVO qcAiAgentVO : qcAiAgentVOS) {
                qcAiAgentVO.setAgentLogoDomain(url);
            }
        }
        return qcAiAgentVOS;
    }

    @Override
    public List<QcAiAgentVO> queryInternalAgents(QcAiAgentBaseQuery query) {
        query.setUserId(UserManager.getTenantUser().getUserId());
        List<QcAiAgentVO> qcAiAgentVOS = qcAiAgentMapper.queryInternalAgents(query);
        if (CollectionUtils.isNotEmpty(qcAiAgentVOS)) {
            for (QcAiAgentVO qcAiAgentVO : qcAiAgentVOS) {
                qcAiAgentVO.setAgentLogoDomain(url);
            }
        }
        return qcAiAgentVOS;
    }

    @Override
    public AgentsClientQueryVO queryClientAgents(List<Long> deptIds) {
        QcAiAgentQuery baseQuery = new QcAiAgentQuery();
        baseQuery.setDeptIdList(deptIds);
        baseQuery.setUserId(RequestHolder.getThreadLocalUser().getUserId());
        List<QcAiAgentVO> list = queryAllAgents(baseQuery);
        return AgentsClientQueryVO.builder()
                .recommendAgents(list)
                .recentUseAgents(List.of())
                .build();
    }

    @Override
    public List<QcAiAgentVO> queryAllAgents(QcAiAgentQuery query) {
        // query.setViewAll(checkCanViewAll());
        List<QcAiAgentVO> qcAiAgentVOS = qcAiAgentMapper.queryAllAgents(query);
        if (CollectionUtils.isNotEmpty(qcAiAgentVOS)) {
            for (QcAiAgentVO qcAiAgentVO : qcAiAgentVOS) {
                if (QcAiAgent.INTERNAL_AGENT.equals(qcAiAgentVO.getInternalFlag())) {
                    continue;
                }
                if (StringUtils.isEmpty(qcAiAgentVO.getAgentLogo())) {
                    qcAiAgentVO.setAgentLogo("https://res.waiqin365.com/d/static/agent/aiIcon.png");
                } else {
                    qcAiAgentVO.setAgentLogo(String.format("%s/%s", url, qcAiAgentVO.getAgentLogo()));
                }
            }
        }
        return qcAiAgentVOS;
    }

    private boolean checkCanViewAll() {
        Map<String, String> extraHeaders = Maps.newHashMap();
        if (StringUtils.isNotEmpty(RequestHolder.getRequestCookie())) {
            extraHeaders.put("Cookie", RequestHolder.getRequestCookie());
        } else if (StringUtils.isNotEmpty(RequestHolder.getRequestToken())) {
            extraHeaders.put("Authorization", RequestHolder.getRequestToken());
        }

        Map<String, String> request = Maps.newHashMap();
        request.put("menu_id", "7754980688846664957");
        try {
            String s = HttpUtil.postForm(String.format("%s/%s", appsvrDominUrl, "/platform/menu/v1/getMenuOpCodes.do"),
                    request, extraHeaders);
            if (StringUtils.isEmpty(s)) {
                return false;
            }

            JSONArray data = JSONObject.parseObject(s).getJSONArray("data");
            return data.contains("MANAGE_AGENT");
        } catch (Exception e) {
            log.error("调用getMenuOpCodes异常", e);
            return false;
        }
    }

    @Override
    public void publish(QcAiAgentPublishDTO publishDTO) {
        qcAiAgentMapper.publish(publishDTO.getId(), publishDTO.getUserId(), publishDTO.getUserName(),
                publishDTO.getShowChatLogContentType());
        authorityDetail(publishDTO);
    }

    @Override
    public void deactivate(Long id, Long userId, String userName) {
        qcAiAgentMapper.deactivate(id, userId, userName);
    }

    @Override
    public QcAiAgentDetailVO queryAgentDetail(Long id) {
        QcAiAgentDetailVO qcAiAgentDetailVO = qcAiAgentMapper.queryAgentDetail(id);
        List<QcAiAgentAuthorityDistributeDetail> authorityDistributeDetailList = qcAiAgentMapper
                .selectAuthorityDistributeDetailList(id);
        qcAiAgentDetailVO.setAuthorityDistributeDetailList(authorityDistributeDetailList);
        List<QcAiAgentIntentRecognition> intentList = qcAiAgentMapper.selectIntentList(id);
        List<QcAiAgentExtConfig> extConfigs = qcAiAgentMapper.selectExtConfigByAgentId(id);
        Map<String, String> extConfigMap = new HashMap<>();
        if (extConfigs != null) {
            for (QcAiAgentExtConfig config : extConfigs) {
                if (config.getConfigKey() != null) {
                    extConfigMap.put(config.getConfigKey(), config.getConfigValue());
                }
            }
        }

        boolean isIncludeOther = intentList.stream()
                .anyMatch(item -> item.getId() == 1L || Objects.equals(item.getIntentCode(), "OTHER"));
        if (!isIncludeOther) {
            QcAiAgentIntentRecognition otherIntent = new QcAiAgentIntentRecognition();
            otherIntent.setId(1L);
            otherIntent.setCreateTime(LocalDateTime.now());
            otherIntent.setIntentName("其他");
            otherIntent.setIntentCode("OTHER");
            otherIntent.setMaxRecallCount(5);
            otherIntent.setMinMatchThreshold(0.5);
            otherIntent.setQaMinMatchThreshold(0.9);
            otherIntent.setSearchScope("2");
            otherIntent.setRelateKnowledgeList(List.of());
            otherIntent.setRelateFileList(List.of());
            intentList.add(otherIntent);
        }
        qcAiAgentDetailVO.setIntentList(intentList);
        qcAiAgentDetailVO.setLogoDomain(url);
        qcAiAgentDetailVO.setExtConfigs(extConfigMap);
        List<QcKnowledgeInfo> qcKnowledgeInfos = knowledgeInfoMapper.selectByAgentId(id);
        if (CollectionUtils.isNotEmpty(qcKnowledgeInfos)) {
            qcAiAgentDetailVO.setKnowledge(qcKnowledgeInfos.stream()
                    .map(item -> QcAiAgentDetailVO.AgentKnowledge.builder()
                            .id(item.getId())
                            .status(item.getStatus())
                            .publishFlag(item.getPublishFlag())
                            .knowledgeId(item.getId())
                            .knowledgeName(item.getKnowledgeName())
                            .knowledgeFaceFullPath(item.getKnowledgeFaceFullPath() == null ? ""
                                    : fileProperty.getUrl() + "/" + item.getKnowledgeFaceFullPath())
                            .build())
                    .collect(Collectors.toList()));
        }

        return qcAiAgentDetailVO;
    }

    @Override
    public void enable(Long id, Long userId, String userName) {
        qcAiAgentMapper.enableAgent(id, userId, userName);
    }

    @Override
    public void disable(Long id, Long userId, String userName) {
        qcAiAgentMapper.disableAgent(id, userId, userName);
    }

    @Override
    public void delete(Long id, Long userId, String userName) {
        qcAiAgentMapper.deleteAgent(id, userId, userName);
    }

    @Override
    public void generateLeadingQuestionsForExistingTenants(Long tenantId) {
        String condition = "";
        if (tenantId != null) {
            condition = "dd.id = " + tenantId;
        }
        List<DatasourceConfig> configs = datasourceService.queryDataSourceWithSqlClause(condition);
        if (CollectionUtils.isEmpty(configs)) {
            log.info("没有找到任何租户数据源配置");
            return;
        }
        log.info("找到{}个租户数据源配置", configs.size());
        TenantUser tenantUser = new TenantUser();

        List<Long> successTenants = new ArrayList<>();
        List<Long> failedTenants = new ArrayList<>();

        for (DatasourceConfig config : configs) {
            try {
                if (leadingQuestionService.checkAgentLeadingQuestions(config, PRODUCT_TALK_AGENT_ID)) {
                    continue;
                }
                tenantUser.setTenantId(config.getId());
                RequestHolder.setThreadLocalUser(tenantUser);
                List<String> products = leadingQuestionService.queryProductInfo();
                if (products.isEmpty()) {
                    log.info("租户{}没有产品信息，跳过", config.getId());
                    continue;
                }
                // 生成问题
                String questions = leadingQuestionService.generateQuestions(products);
                // 更新到数据库（针对这个租户的数据源）
                leadingQuestionService.saveQuestions(config, questions);
                log.info("租户{}的预置问题生成并更新成功", config.getId());
                successTenants.add(config.getId());
            } catch (Exception e) {
                log.error("租户{}预置问题生成失败", config.getId(), e);
                failedTenants.add(config.getId());
            }
        }

        log.info("预置问题生成并更新成功的租户: {}", successTenants);
        log.info("预置问题生成失败的租户: {}", failedTenants);

    }

    @Override
    public boolean showInVisitTask(List<Long> deptIds) {
        Long userId = RequestHolder.getThreadLocalUser().getUserId();
        // 查询权限表和配置表
        List<QcAiAgentExtConfig> extConfigList = qcAiAgentMapper.selectExtConfigByAgentId(6L);
        // 判断extConfigList中是否有showInVisitTask
        AtomicBoolean hasShowInVisitTask = new AtomicBoolean(false);
        extConfigList.stream()
                .filter(config -> "showInVisitTask".equals(config.getConfigKey()))
                .findFirst()
                .ifPresent(config -> {
                    if ("1".equals(config.getConfigValue())) {
                        // 如果值为1，当前用户是否授权了agent=6L
                        Long count = qcAiAgentAuthorityDistributeDetailMapper.isUserAuthorizedForAgent(userId, 6L,
                                deptIds);
                        if (count != null && count > 0) {
                            hasShowInVisitTask.set(true);
                        }
                    }
                });
        return hasShowInVisitTask.get();
    }

    /**
     * 保存客户洞察配置
     *
     * @param agent   智能体保存DTO
     * @param agentId 智能体ID
     */
    private void saveCustomerInsightConfig(QcAiAgentSaveDTO agent, Long agentId) {
        try {
            // 构建客户洞察配置工作区DTO
            InsightConfigWorkspaceDTO workspaceDTO = new InsightConfigWorkspaceDTO();
            workspaceDTO.setAgentId(agentId);
            workspaceDTO.setDimensionConfigurations(agent.getInsightDimensionConfigurations());
            workspaceDTO.setSummaryConfiguration(agent.getInsightSummaryConfiguration());

            // 调用客户洞察服务保存配置
            customerInsightService.saveWorkspace(workspaceDTO);
        } catch (Exception e) {
            log.warn("保存客户洞察配置失败，agentId: {}, error: {}", agentId, e.getMessage());
            // 不抛出异常，避免影响智能体基本信息的保存
        }
    }
}
