package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 洞察总结配置数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InsightSummaryConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总结配置ID（已废弃，不需要传递，后端通过agentId自动处理）
     */
    private Long id;

    private String comprehensivePrompt;

    /**
     * 综合分析结构化提示词片段
     */
    private List<PromptFragmentDTO> comprehensivePromptFragments;

    private String summaryAdvicePrompt;

    /**
     * 建议结构化提示词片段
     */
    private List<PromptFragmentDTO> summaryAdvicePromptFragments;

    /**
     * 关联的衡量标准列表（支持增删改查）
     */
    private List<InsightStandardDTO> standards;
}