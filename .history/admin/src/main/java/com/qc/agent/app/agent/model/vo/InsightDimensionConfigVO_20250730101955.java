package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.InsightStandard;

import lombok.Data;

/**
 * 维度配置视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightDimensionConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度配置 ID
     */
    private Long id;

    /**
     * 关联的智能体 ID
     */
    private Long agentId;

    /**
     * 维度编码
     */
    private String dimensionCode;

    /**
     * 维度展示名称
     */
    private String dimensionName;

    /**
     * 大模型解读提示词
     */
    private String interpretationPrompt;

    /**
     * 结构化提示词片段
     */
    private List<PromptFragmentDTO> interpretationPromptFragments;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 关联的衡量标准列表
     */
    private List<InsightStandard> standards;

    /**
     * 该维度下的数据源数组
     */
    private List<InsightDataSourceVO> dataSources;

    private String status;
}