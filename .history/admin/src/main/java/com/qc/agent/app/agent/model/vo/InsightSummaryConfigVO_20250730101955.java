package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.InsightStandard;

import lombok.Data;

/**
 * 洞察总结配置视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightSummaryConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long agentId;

    private String comprehensivePrompt;

    /**
     * 综合分析结构化提示词片段
     */
    private List<PromptFragmentDTO> comprehensivePromptFragments;

    private String summaryAdvicePrompt;

    /**
     * 建议结构化提示词片段
     */
    private List<PromptFragmentDTO> summaryAdvicePromptFragments;

    private List<InsightStandard> standards;
}