package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightPromptItem;

/**
 * 洞察提示词项Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightPromptItemMapper {

    /**
     * 根据ID查询提示词项
     */
    InsightPromptItem selectById(@Param("id") Long id);

    /**
     * 根据配置ID和类型查询提示词项列表
     */
    List<InsightPromptItem> selectByConfigIdAndType(@Param("configId") Long configId,
            @Param("configType") String configType);

    /**
     * 根据智能体ID和配置类型查询提示词项列表
     */
    List<InsightPromptItem> selectByAgentIdAndConfigType(@Param("agentId") Long agentId,
            @Param("configType") String configType);

    /**
     * 插入提示词项
     */
    int insert(InsightPromptItem promptItem);

    /**
     * 批量插入提示词项
     */
    int batchInsert(@Param("list") List<InsightPromptItem> promptItems);

    /**
     * 根据ID更新提示词项
     */
    int updateById(InsightPromptItem promptItem);

    /**
     * 根据配置ID和类型删除提示词项
     */
    int deleteByConfigIdAndType(@Param("configId") Long configId, @Param("configType") String configType);

    /**
     * 根据智能体ID删除提示词项
     */
    int deleteByAgentId(@Param("agentId") Long agentId);
}