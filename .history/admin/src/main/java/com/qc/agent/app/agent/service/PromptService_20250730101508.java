package com.qc.agent.app.agent.service;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;

/**
 * 提示词服务接口
 *
 * <AUTHOR>
 */
public interface PromptService {

    /**
     * 根据配置ID和类型获取主提示词
     */
    MainPromptDTO getMainPromptByConfigIdAndType(Long configId, String promptType);

    /**
     * 保存主提示词及其片段
     */
    boolean saveMainPrompt(MainPromptDTO mainPromptDTO);

    /**
     * 根据主提示词ID获取片段列表
     */
    List<PromptFragmentDTO> getFragmentsByMainPromptId(Long mainPromptId);

    /**
     * 将片段列表转换为JSON对象
     */
    JsonNode fragmentsToJsonObject(List<PromptFragmentDTO> fragments);

    /**
     * 将JSON对象转换为片段列表
     */
    List<PromptFragmentDTO> jsonObjectToFragments(JsonNode jsonNode, Long mainPromptId);

    /**
     * 根据片段生成完整提示词（替换变量）
     */
    String generatePromptFromFragments(List<PromptFragmentDTO> fragments, Map<String, Object> variables);
} 