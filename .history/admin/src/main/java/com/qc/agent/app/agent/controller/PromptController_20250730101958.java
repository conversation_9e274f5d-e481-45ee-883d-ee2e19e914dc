package com.qc.agent.app.agent.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.service.PromptService;
import com.qc.agent.common.core.Message;

import lombok.extern.slf4j.Slf4j;

/**
 * 提示词管理Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ai-agent/prompt")
public class PromptController {

    @Resource
    private PromptService promptService;

    /**
     * 根据配置ID和类型获取主提示词
     */
    @GetMapping("/getByConfigIdAndType")
    public Message getMainPromptByConfigIdAndType(
            @RequestParam("configId") Long configId,
            @RequestParam("promptType") String promptType) {
        try {
            MainPromptDTO mainPromptDTO = promptService.getMainPromptByConfigIdAndType(configId, promptType);
            return Message.of().data(mainPromptDTO).ok();
        } catch (Exception e) {
            log.error("获取主提示词失败: configId={}, promptType={}", configId, promptType, e);
            return Message.of().error("获取主提示词失败: " + e.getMessage());
        }
    }

    /**
     * 保存主提示词及其片段
     */
    @PostMapping("/save")
    public Message saveMainPrompt(@RequestBody MainPromptDTO mainPromptDTO) {
        try {
            boolean result = promptService.saveMainPrompt(mainPromptDTO);
            return Message.of().data(result).ok();
        } catch (Exception e) {
            log.error("保存主提示词失败", e);
            return Message.of().error("保存主提示词失败: " + e.getMessage());
        }
    }

    /**
     * 根据主提示词ID获取片段列表
     */
    @GetMapping("/getFragments")
    public Message getFragmentsByMainPromptId(
            @RequestParam("mainPromptId") Long mainPromptId) {
        try {
            List<PromptFragmentDTO> fragments = promptService.getFragmentsByMainPromptId(mainPromptId);
            return Message.of().data(fragments).ok();
        } catch (Exception e) {
            log.error("获取提示词片段失败: mainPromptId={}", mainPromptId, e);
            return Message.of().error("获取提示词片段失败: " + e.getMessage());
        }
    }

    /**
     * 将片段列表转换为JSON对象
     */
    @PostMapping("/fragmentsToJson")
    public Message fragmentsToJsonObject(@RequestBody List<PromptFragmentDTO> fragments) {
        try {
            JsonNode jsonNode = promptService.fragmentsToJsonObject(fragments);
            return Message.of().data(jsonNode).ok();
        } catch (Exception e) {
            log.error("片段转JSON失败", e);
            return Message.of().error("片段转JSON失败: " + e.getMessage());
        }
    }

    /**
     * 将JSON对象转换为片段列表
     */
    @PostMapping("/jsonToFragments")
    public Message jsonObjectToFragments(
            @RequestBody Map<String, Object> request) {
        try {
            JsonNode jsonNode = (JsonNode) request.get("jsonNode");
            Long mainPromptId = Long.valueOf(request.get("mainPromptId").toString());
            List<PromptFragmentDTO> fragments = promptService.jsonObjectToFragments(jsonNode, mainPromptId);
            return Message.of().data(fragments).ok();
        } catch (Exception e) {
            log.error("JSON转片段失败", e);
            return Message.of().error("JSON转片段失败: " + e.getMessage());
        }
    }

    /**
     * 根据片段生成完整提示词（替换变量）
     */
    @PostMapping("/generatePrompt")
    public Message generatePromptFromFragments(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<PromptFragmentDTO> fragments = (List<PromptFragmentDTO>) request.get("fragments");
            @SuppressWarnings("unchecked")
            Map<String, Object> variables = (Map<String, Object>) request.get("variables");
            String prompt = promptService.generatePromptFromFragments(fragments, variables);
            return Message.of().data(prompt).ok();
        } catch (Exception e) {
            log.error("生成提示词失败", e);
            return Message.of().error("生成提示词失败: " + e.getMessage());
        }
    }
}