package com.qc.agent.app.agent.service.impl;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qc.agent.app.agent.enums.DimensionCode;
import com.qc.agent.app.agent.mapper.InsightConfigStandardRelMapper;
import com.qc.agent.app.agent.mapper.InsightDataItemMapper;
import com.qc.agent.app.agent.mapper.InsightDataSourceMapper;
import com.qc.agent.app.agent.mapper.InsightDimensionConfigMapper;
import com.qc.agent.app.agent.mapper.InsightDimensionRefItemRelMapper;
import com.qc.agent.app.agent.mapper.InsightStandardMapper;
import com.qc.agent.app.agent.mapper.InsightSummaryConfigMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.dto.InsightConfigWorkspaceDTO;
import com.qc.agent.app.agent.model.dto.InsightDataSourceDTO;
import com.qc.agent.app.agent.model.dto.InsightDimensionConfigDTO;
import com.qc.agent.app.agent.model.dto.InsightStandardDTO;
import com.qc.agent.app.agent.model.dto.InsightSummaryConfigDTO;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.entity.InsightConfigStandardRel;
import com.qc.agent.app.agent.model.entity.InsightDataItem;
import com.qc.agent.app.agent.model.entity.InsightDataSource;
import com.qc.agent.app.agent.model.entity.InsightDimensionConfig;
import com.qc.agent.app.agent.model.entity.InsightDimensionRefItemRel;
import com.qc.agent.app.agent.model.entity.InsightStandard;
import com.qc.agent.app.agent.model.entity.InsightSummaryConfig;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.app.agent.model.vo.InsightConfigWorkspaceVO;
import com.qc.agent.app.agent.model.vo.InsightDataItemGroup;
import com.qc.agent.app.agent.model.vo.InsightDataSourceVO;
import com.qc.agent.app.agent.model.vo.InsightDimensionConfigVO;
import com.qc.agent.app.agent.model.vo.InsightSummaryConfigVO;
import com.qc.agent.app.agent.service.CustomerInsightService;
import com.qc.agent.app.agent.service.PromptService;
import com.qc.agent.app.agent.service.customer_insight_intent.CustomerInsightIntentHandler;
import com.qc.agent.app.agent.util.InsightDataItemTreeUtils;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.llm.CustomerInsightIntentClassifier;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.CustomerTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAQa;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户洞察服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerInsightServiceImpl implements CustomerInsightService {

    private final QcAiAgentMapper qcAiAgentMapper;
    private final QcAiAgentModelMapper qcAiAgentModelMapper;
    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;

    private final CustomerInsightIntentClassifier customerInsightIntentClassifier;
    private final Map<CustomerInsightIntent, CustomerInsightIntentHandler> customerInsightIntentHandlerMap;
    private final CustomerTool customerTool;

    private static final String CONFIG_TYPE_DIMENSION = "DIMENSION";
    private static final String CONFIG_TYPE_SUMMARY = "SUMMARY";

    @Resource
    private InsightDimensionConfigMapper insightDimensionConfigMapper;
    @Resource
    private InsightDimensionRefItemRelMapper insightDimensionRefItemRelMapper;
    @Resource
    private InsightConfigStandardRelMapper insightConfigStandardRelMapper;
    @Resource
    private InsightDataItemMapper insightDataItemMapper;
    @Resource
    private InsightStandardMapper insightStandardMapper;
    @Resource
    private InsightDataSourceMapper insightDataSourceMapper;

    @Resource
    private InsightSummaryConfigMapper insightSummaryConfigMapper;

    @Resource
    private PromptService promptService;

    @Override
    public Map<String, Object> intent(QcAiCustomerInsightAssistantQuery query) {
        final String logPrefix = LLMTools.getLogPrefix(query.getAgentId(), UUID.randomUUID().toString());
        log.info("{} ===== 开始处理客户洞察意图识别请求 =====", logPrefix);

        // 步骤 1: 处理特殊情况，如用户点击 "确定"
        return handleConfirmClick(query, logPrefix).orElseGet(() -> {

            // 步骤 2: 加载并验证执行所需的前提条件（Agent, Model）
            Prerequisites prerequisites = loadPrerequisites(query.getAgentId(), logPrefix);

            // 步骤 3: 分类并精炼用户意图
            CustomerInsightBusinessIntent finalIntent = classifyAndRefineIntent(query, prerequisites, logPrefix);

            // 步骤 4: 根据最终意图生成回答并持久化会话
            ConversationResult conversationResult = generateAnswerAndPersist(query, finalIntent, logPrefix);

            // 步骤 5: 构建并返回最终响应
            Map<String, Object> response = buildResponse(finalIntent, conversationResult, logPrefix);
            log.info("{} ===== 客户洞察意图识别请求处理完成 =====", logPrefix);
            return response;
        });
    }

    /**
     * 处理用户点击"确定"按钮的快捷路径
     */
    private Optional<Map<String, Object>> handleConfirmClick(QcAiCustomerInsightAssistantQuery query,
            String logPrefix) {
        if ("1".equals(query.getIsClick())) {
            log.info("{} 用户点击了确定按钮，更新上一个会话", logPrefix);

            // 更新上一个会话
            if (query.getConversationId() != null) {
                QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(query.getConversationId());
                if (conversation != null) {
                    // 增加空指针判断，避免NPE
                    String customer = "";
                    try {
                        if (query.getCommonParam() != null
                                && query.getCommonParam().getInsightIntent() != null
                                && query.getCommonParam().getInsightIntent().entities() != null
                                && query.getCommonParam().getInsightIntent().entities()
                                        .get("customer") != null) {
                            customer = query.getCommonParam().getInsightIntent().entities().get("customer")
                                    .toString();
                        }
                    } catch (Exception e) {
                        log.error("{} 获取客户信息时发生异常: {}", logPrefix, e.getMessage(), e);
                        customer = "";
                    }
                    conversation.setAnswer("没问题，请提供需要洞察的客户，请选择或者直接告诉我都行。客户选择:" + customer);
                    conversation.setAnswerTime(LocalDateTime.now());
                    qcAiAgentConversationMapper.update(conversation);
                }
            }

            // 返回"其他"意图的响应
            // 使用Map.of创建不可变Map，更安全简洁
            Map<String, Object> response = Map.of("insightIntent", Map.of("type", CustomerInsightIntent.OTHER,
                    "entities", new JSONObject()),
                    "isDealer", customerTool.queryClientData(logPrefix),
                    "conversationId", String.valueOf(query.getConversationId()) // 确保类型一致
            );
            log.info("{} 构建响应完成: intent={}, conversationId={}",
                    logPrefix, CustomerInsightIntent.OTHER, query.getConversationId());
            return Optional.of(response);
        }
        return Optional.empty();
    }

    /**
     * 加载并验证Agent和Model是否存在。
     * 建议: 考虑为 "Not Found" 情况创建自定义的、更具体的业务异常。
     */
    private Prerequisites loadPrerequisites(Long agentId, String logPrefix) {
        log.debug("{} 正在加载Agent和Model...", logPrefix);
        QcAiAgent qcAiAgent = Optional.ofNullable(qcAiAgentMapper.selectById(agentId)).orElseThrow(() -> {
            log.error("{} 未能找到ID为: {} 的智能体，处理中断。", logPrefix, agentId);
            return new IllegalArgumentException("Agent not found with id: " + agentId);
        });

        QcAiAgentModel qcAiAgentModel = Optional.ofNullable(qcAiAgentModelMapper.selectById(qcAiAgent.getModelId()))
                .orElseThrow(() -> {
                    log.error("{} 未能找到ID为: {} 的模型，处理中断。", logPrefix, qcAiAgent.getModelId());
                    return new IllegalStateException("Model not found for agent with id: " + agentId);
                });

        log.debug("{} Agent和Model加载成功。", logPrefix);
        return new Prerequisites(qcAiAgent, qcAiAgentModel);
    }

    private LLARequest initLLARequest(QcAiCustomerInsightAssistantQuery query) {
        List<ConversationVO> conversationVOS = new ArrayList<>();
        if (!Objects.equals(query.getIsNew(), "1")) {
            conversationVOS = qcAiAgentConversationMapper.queryAgentConversation(query.getAgentId(),
                    UserManager.getTenantUser().getUserId(), 1, 0, "1");
        }
        return LLARequest.builder().sessionId(query.getSessionId())
                .content(query.getQuestion())
                .qas(Optional.ofNullable(conversationVOS).orElse(Lists.newArrayList())
                        .stream()
                        .map((ConversationVO item) -> LLAQa.builder().question(item.getQuestion())
                                .answer(item.getAnswer()).build())
                        .collect(Collectors.toList()))
                .build();

    }

    /**
     * 分类并精炼用户意图
     */
    private CustomerInsightBusinessIntent classifyAndRefineIntent(QcAiCustomerInsightAssistantQuery query,
            Prerequisites prerequisites, String logPrefix) {
        log.info("{} 开始意图分类，问题: {}", logPrefix, query.getQuestion());
        LLAConfig config = LLMTools.initLLAConfig(prerequisites.model());
        LLARequest request = initLLARequest(query);

        log.info("{} 向LLM发送初步意图分类请求...", logPrefix);
        // 使用意图分类器进行初步分类
        CustomerInsightBusinessIntent initialIntent = customerInsightIntentClassifier.classifyIntent(request, config,
                logPrefix);
        log.info("{} LLM初步分类意图为: '{}', 实体: {}", logPrefix, initialIntent.intent(), initialIntent.entities());

        // 根据意图类型查找对应的处理器进行精炼
        return Optional.ofNullable(customerInsightIntentHandlerMap.get(initialIntent.intent())).map(handler -> {
            log.info("{} 找到意图 '{}' 的特定处理器, 开始精炼...", logPrefix, initialIntent.intent());
            CustomerInsightBusinessIntent handledIntent = handler.handle(initialIntent, query, logPrefix);
            log.info("{} 处理器精炼后的意图为: '{}', 实体: {}", logPrefix, handledIntent.intent(), handledIntent.entities());
            return handledIntent;
        }).orElse(initialIntent);
    }

    /**
     * 根据最终意图生成回答并持久化会话
     */
    private ConversationResult generateAnswerAndPersist(QcAiCustomerInsightAssistantQuery query,
            CustomerInsightBusinessIntent finalIntent, String logPrefix) {
        // 创建会话记录
        QcAiAgentConversation conversation = new QcAiAgentConversation();
        conversation.setId(UUIDUtils.getUUID2Long());
        conversation.setAgentId(query.getAgentId());
        conversation.setQuestion(query.getQuestion());
        conversation.setCreateTime(LocalDateTime.now());
        conversation.setSessionId(query.getSessionId());
        conversation.setChatSessionId(query.getChatSessionId());
        conversation.setSource(query.getSource());
        conversation.setStatus("0");
        conversation.setConversationStatus("0");

        String answer = "";
        if (Objects.equals(finalIntent.intent(), CustomerInsightIntent.CUSTOMER_ANALYSIS_INPUT)) {
            // 如果customer字段为null，则取值为""
            String customerName = finalIntent.entities().getString("customer");
            if (customerName == null) {
                customerName = "";
            }
            answer = "没问题，请提供需要洞察的客户，请选择或者直接告诉我都行。客户选择:" + customerName;
        }

        // 更新会话记录
        conversation.setAnswer(answer);
        conversation.setAnswerTime(LocalDateTime.now());
        qcAiAgentConversationMapper.insert(conversation);

        return new ConversationResult(answer, conversation.getId().toString());
    }

    /**
     * 构建并返回最终响应
     */
    private Map<String, Object> buildResponse(CustomerInsightBusinessIntent finalIntent,
            ConversationResult conversationResult, String logPrefix) {
        // 使用Map.of创建不可变Map，更安全
        Map<String, Object> response = Map.of("insightIntent",
                Map.of("type", finalIntent.intent(),
                        "entities", finalIntent.entities()),
                "isDealer", customerTool.queryClientData(logPrefix),
                "conversationId", conversationResult.conversationId());

        log.info("{} 构建响应完成: intent={}, conversationId={}",
                logPrefix, finalIntent.intent(), conversationResult.conversationId());
        return response;
    }

    /**
     * 定义一个Record来封装Agent和Model，作为不可变数据载体。
     */
    private record Prerequisites(QcAiAgent agent, QcAiAgentModel model) {
    }

    /**
     * 用于封装答案和会话ID的结果类。
     */
    private record ConversationResult(String answer, String conversationId) {
    }

    @Override
    public InsightConfigWorkspaceVO getWorkspace(Long agentId) {
        InsightConfigWorkspaceVO workspaceVO = new InsightConfigWorkspaceVO();

        // 1. 查询所有维度配置
        List<InsightDimensionConfig> dimensionConfigs = insightDimensionConfigMapper.selectByAgentId(agentId);
        List<InsightDimensionConfigVO> dimensionVOs = new ArrayList<>();
        for (InsightDimensionConfig dim : dimensionConfigs) {
            InsightDimensionConfigVO dimVO = buildDimensionConfigVO(dim);

            // 查询维度关联的数据项关系
            List<InsightDimensionRefItemRel> dimensionItemRels = insightDimensionRefItemRelMapper
                    .selectByDimensionConfigId(dim.getId());

            // 按数据源分组收集选中的数据项
            Map<String, List<InsightDimensionRefItemRel>> dataSourceGroups = dimensionItemRels.stream()
                    .collect(Collectors.groupingBy(rel -> {
                        InsightDataItem dataItem = insightDataItemMapper.selectById(rel.getRefDataItemId());
                        return dataItem != null ? dataItem.getQueryBusinessCode() : null;
                    }));

            // 根据数据源分组构建数据源VO
            List<InsightDataSourceVO> dataSourceVOs = new ArrayList<>();
            for (Map.Entry<String, List<InsightDimensionRefItemRel>> entry : dataSourceGroups.entrySet()) {
                String sourceCode = entry.getKey();
                List<InsightDimensionRefItemRel> rels = entry.getValue();

                if (sourceCode != null && !rels.isEmpty()) {
                    // 查询数据源信息
                    List<InsightDataSource> dataSources = insightDataSourceMapper.selectBySourceCode(sourceCode);
                    if (!dataSources.isEmpty()) {
                        InsightDataSource dataSource = dataSources.get(0);
                        InsightDataSourceVO dsVO = new InsightDataSourceVO();
                        BeanUtils.copyProperties(dataSource, dsVO);

                        // 获取该数据源下的所有数据项（通过 queryBusinessCode 关联）
                        List<InsightDataItem> items = insightDataItemMapper
                                .selectByQueryBusinessCode(dataSource.getSourceCode());

                        // 将平铺的数据项转换为树形结构
                        List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);

                        // 标记所有选中的数据项
                        for (InsightDimensionRefItemRel rel : rels) {
                            markSelectedDataItems(treeItems, rel.getRefDataItemId());
                        }

                        // 收集所有选中的数据项，保持父子关系
                        List<InsightDataItem> selectedItems = new ArrayList<>();
                        for (InsightDimensionRefItemRel rel : rels) {
                            InsightDataItem selectedItem = findSelectedDataItem(treeItems, rel.getRefDataItemId());
                            if (selectedItem != null) {
                                // 检查是否已经添加过（避免重复）
                                boolean alreadyAdded = selectedItems.stream()
                                        .anyMatch(item -> item.getId().equals(selectedItem.getId()));
                                if (!alreadyAdded) {
                                    // 检查是否是子项，如果是子项且父项已经被选中，则跳过（避免重复）
                                    if (selectedItem.getParentId() != null) {
                                        boolean parentAlreadySelected = selectedItems.stream()
                                                .anyMatch(item -> item.getId().equals(selectedItem.getParentId()));
                                        if (parentAlreadySelected) {
                                            continue; // 跳过子项，因为父项已经包含了它
                                        }
                                    }
                                    InsightDataItem copiedItem = createSelectedDataItemCopy(selectedItem);
                                    selectedItems.add(copiedItem);
                                }
                            }
                        }

                        dsVO.setSelectedDataItems(selectedItems);

                        // 设置查询参数值（使用第一个关系的值，因为同一数据源的queryValue应该相同）
                        dsVO.setQueryValue(rels.get(0).getQueryValue());

                        dataSourceVOs.add(dsVO);
                    }
                }
            }
            dimVO.setDataSources(dataSourceVOs);
            dimensionVOs.add(dimVO);
        }
        workspaceVO.setCurrentDimensionConfiguration(dimensionVOs);

        // 2. 获取当前总结配置
        InsightSummaryConfig summaryConfig = insightSummaryConfigMapper.selectByAgentId(agentId);
        if (summaryConfig != null) {
            workspaceVO.setCurrentSummaryConfiguration(buildSummaryConfigVO(summaryConfig));
        }

        // 3. 获取所有可用选项
        InsightConfigWorkspaceVO.AvailableOptions options = new InsightConfigWorkspaceVO.AvailableOptions();

        // 按维度分组数据源
        List<InsightDataSource> allDataSources = insightDataSourceMapper.selectAll();
        Map<String, List<InsightDataSource>> dataSourcesByDimension = allDataSources.stream()
                .collect(Collectors.groupingBy(InsightDataSource::getBelongDimensionCode));

        // 构建维度下的数据源结构
        List<InsightDimensionConfigVO> availableDimensionVOs = new ArrayList<>();
        for (Map.Entry<String, List<InsightDataSource>> entry : dataSourcesByDimension.entrySet()) {
            String dimensionCode = entry.getKey();
            List<InsightDataSource> dataSources = entry.getValue();

            // 创建维度VO（不包含配置信息，只用于展示可用选项）
            InsightDimensionConfigVO dimensionVO = new InsightDimensionConfigVO();
            dimensionVO.setDimensionCode(dimensionCode);
            dimensionVO.setDimensionName(dataSources.get(0).getBelongDimensionName()); // 取第一个数据源的维度名称

            // 构建数据源VO列表
            List<InsightDataSourceVO> dataSourceVOs = dataSources.stream().map(dataSource -> {
                InsightDataSourceVO dsVO = new InsightDataSourceVO();
                BeanUtils.copyProperties(dataSource, dsVO);

                // 获取该数据源下的所有数据项（通过 queryBusinessCode 关联）
                List<InsightDataItem> items = insightDataItemMapper
                        .selectByQueryBusinessCode(dataSource.getSourceCode());

                // 将平铺的数据项转换为树形结构
                List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);

                // 按 dataTypeCode 分组数据项，支持前端分层选择
                Map<String, List<InsightDataItem>> itemsByTypeMap = treeItems.stream()
                        .collect(Collectors.groupingBy(InsightDataItem::getDataTypeCode));

                // 转换为数组结构，创建新的对象避免循环引用
                List<InsightDataItemGroup> itemsByType = itemsByTypeMap.entrySet().stream()
                        .map(mapEntry -> {
                            // 为每个数据项创建新的副本，避免循环引用
                            List<InsightDataItem> copiedItems = mapEntry.getValue().stream()
                                    .map(item -> {
                                        InsightDataItem copiedItem = new InsightDataItem();
                                        BeanUtils.copyProperties(item, copiedItem);
                                        // 清空children避免循环引用
                                        copiedItem.setChildren(null);
                                        return copiedItem;
                                    })
                                    .collect(Collectors.toList());
                            return new InsightDataItemGroup(mapEntry.getKey(), null, copiedItems);
                        })
                        .collect(Collectors.toList());

                dsVO.setDataItemsByType(itemsByType);
                return dsVO;
            }).collect(Collectors.toList());

            dimensionVO.setDataSources(dataSourceVOs);
            availableDimensionVOs.add(dimensionVO);
        }

        options.setDimensions(availableDimensionVOs);
        workspaceVO.setAvailableOptions(options);

        return workspaceVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWorkspace(InsightConfigWorkspaceDTO workspaceDTO) {
        Long agentId = workspaceDTO.getAgentId();

        // === 1. 处理维度配置（新增、修改、删除） ===
        if (workspaceDTO.getDimensionConfigurations() != null) {
            // 获取当前已存在的维度配置
            List<InsightDimensionConfig> existingConfigs = insightDimensionConfigMapper.selectByAgentId(agentId);
            List<Long> existingConfigIds = existingConfigs.stream()
                    .map(InsightDimensionConfig::getId)
                    .toList();

            // 获取请求中的配置ID
            List<Long> requestConfigIds = workspaceDTO.getDimensionConfigurations().stream()
                    .map(InsightDimensionConfigDTO::getId)
                    .filter(Objects::nonNull)
                    .toList();

            // 找出需要删除的配置（存在但请求中没有的）
            List<Long> configsToDelete = existingConfigIds.stream()
                    .filter(id -> !requestConfigIds.contains(id))
                    .collect(Collectors.toList());

            // 删除配置及其关联关系
            if (!CollectionUtils.isEmpty(configsToDelete)) {
                insightDimensionConfigMapper.deleteBatchIds(configsToDelete);
                insightDimensionRefItemRelMapper.deleteByDimensionConfigIds(configsToDelete);
                insightConfigStandardRelMapper.deleteByConfigIdsAndType(configsToDelete, CONFIG_TYPE_DIMENSION);
            }

            // 处理新增和修改的配置
            for (int i = 0; i < workspaceDTO.getDimensionConfigurations().size(); i++) {
                InsightDimensionConfigDTO dto = workspaceDTO.getDimensionConfigurations().get(i);
                // 按照数组顺序自动设置sortOrder
                dto.setSortOrder(i + 1);

                if (dto.getId() != null) {
                    // 修改现有配置
                    updateDimensionConfig(dto, agentId);
                } else {
                    // 新增配置
                    createDimensionConfig(dto, agentId);
                }
            }
        }

        // === 2. 处理总结配置（新增、修改、删除） ===
        if (workspaceDTO.getSummaryConfiguration() != null) {
            InsightSummaryConfigDTO summaryDTO = workspaceDTO.getSummaryConfiguration();

            // 查询现有总结配置
            InsightSummaryConfig existingSummary = insightSummaryConfigMapper.selectByAgentId(agentId);

            // 判断是否为删除操作（字段为空）
            boolean isDeleteOperation = (summaryDTO.getComprehensivePrompt() == null
                    || summaryDTO.getComprehensivePrompt().trim().isEmpty()) &&
                    (summaryDTO.getSummaryAdvicePrompt() == null
                            || summaryDTO.getSummaryAdvicePrompt().trim().isEmpty())
                    &&
                    (summaryDTO.getStandards() == null || summaryDTO.getStandards().isEmpty());

            if (isDeleteOperation) {
                // 删除总结配置（如果存在）
                if (existingSummary != null) {
                    insightSummaryConfigMapper.deleteByAgentId(agentId);
                    insightConfigStandardRelMapper.deleteByConfigIdsAndType(
                            Collections.singletonList(existingSummary.getId()), CONFIG_TYPE_SUMMARY);
                }
            } else {
                // 新增或更新总结配置
                if (existingSummary != null) {
                    updateSummaryConfig(summaryDTO, agentId);
                } else {
                    createSummaryConfig(summaryDTO, agentId);
                }
            }
        }

        return true;
    }

    /**
     * 保存配置与标准关系（支持新增、修改、删除标准）
     */
    private void saveDimensionStandardRelations(Long configId, List<InsightStandardDTO> standards, String configType) {
        if (standards != null) {
            // 获取当前已存在的标准关系
            List<InsightConfigStandardRel> existingRels = insightConfigStandardRelMapper
                    .selectByConfigIdAndType(configId, configType);
            List<Long> existingStandardIds = existingRels.stream()
                    .map(InsightConfigStandardRel::getStandardId)
                    .toList();

            // 获取请求中的标准ID
            List<Long> requestStandardIds = standards.stream()
                    .map(InsightStandardDTO::getId)
                    .filter(Objects::nonNull)
                    .toList();

            // 找出需要删除的标准（存在但请求中没有的）
            List<Long> standardsToDelete = existingStandardIds.stream()
                    .filter(id -> !requestStandardIds.contains(id))
                    .toList();

            // 删除标准及其关联关系
            if (!CollectionUtils.isEmpty(standardsToDelete)) {
                // 删除关联关系
                insightConfigStandardRelMapper.deleteByConfigIdAndStandardIds(configId, standardsToDelete);
                // 删除标准本身
                insightStandardMapper.deleteBatchIds(standardsToDelete);
            }

            // 处理新增和修改的标准
            for (int i = 0; i < standards.size(); i++) {
                InsightStandardDTO standardDTO = standards.get(i);
                // 按照数组顺序自动设置排序
                standardDTO.setSortOrder(i + 1);

                if (standardDTO.getId() != null) {
                    // 修改现有标准
                    updateStandard(standardDTO);
                    // 确保关联关系存在
                    ensureStandardRelation(configId, standardDTO.getId(), configType);
                } else {
                    // 新增标准
                    createStandardAndRelation(configId, standardDTO, configType);
                }
            }
        }
    }

    /**
     * 新增标准并建立关联关系
     */
    private void createStandardAndRelation(Long configId, InsightStandardDTO standardDTO, String configType) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();
        InsightStandard newStandard = new InsightStandard();
        BeanUtils.copyProperties(standardDTO, newStandard);
        if (CONFIG_TYPE_DIMENSION.equals(configType)) {
            newStandard.setStandardTypeCode("DIMENSION");
            newStandard.setStandardType("维度标准");
        } else if (CONFIG_TYPE_SUMMARY.equals(configType)) {
            newStandard.setStandardTypeCode("SUMMARY");
            newStandard.setStandardType("总结标准");
        }
        Timestamp now = new Timestamp(System.currentTimeMillis());
        newStandard.setStatus("1");
        newStandard.setCreatorId(currentUserId);
        newStandard.setCreatorName(currentUserName);
        newStandard.setCreateTime(now);
        newStandard.setModifyierId(currentUserId);
        newStandard.setModifyierName(currentUserName);
        newStandard.setModifyTime(now);
        insightStandardMapper.insert(newStandard);
        Long standardId = newStandard.getId();
        InsightConfigStandardRel rel = new InsightConfigStandardRel();
        rel.setConfigId(configId);
        rel.setStandardId(standardId);
        rel.setConfigType(configType);
        rel.setStatus("1");
        rel.setCreatorId(currentUserId);
        rel.setCreatorName(currentUserName);
        rel.setCreateTime(now);
        rel.setModifyierId(currentUserId);
        rel.setModifyierName(currentUserName);
        rel.setModifyTime(now);
        insightConfigStandardRelMapper.insert(rel);
    }

    /**
     * 更新标准
     */
    private void updateStandard(InsightStandardDTO standardDTO) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();

        // 先查询现有数据
        InsightStandard existingStandard = insightStandardMapper.selectById(standardDTO.getId());
        if (existingStandard == null) {
            throw new RuntimeException("标准不存在，ID: " + standardDTO.getId());
        }

        // 只更新非null的字段，保持其他字段不变
        InsightStandard standard = new InsightStandard();
        standard.setId(standardDTO.getId());

        // 只更新有值的字段
        if (standardDTO.getStandardName() != null) {
            standard.setStandardName(standardDTO.getStandardName());
        }
        if (standardDTO.getStandardDefinition() != null) {
            standard.setStandardDefinition(standardDTO.getStandardDefinition());
        }
        if (standardDTO.getSortOrder() != null) {
            standard.setSortOrder(standardDTO.getSortOrder());
        }

        // 设置修改相关字段
        standard.setModifyierId(currentUserId);
        standard.setModifyierName(currentUserName);
        standard.setModifyTime(new Timestamp(System.currentTimeMillis()));

        insightStandardMapper.updateStandardInfo(standard);
    }

    /**
     * 确保标准关联关系存在
     */
    private void ensureStandardRelation(Long configId, Long standardId, String configType) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();
        InsightConfigStandardRel existingRel = insightConfigStandardRelMapper.selectByConfigIdAndStandardId(configId,
                standardId);
        if (existingRel == null) {
            InsightConfigStandardRel rel = new InsightConfigStandardRel();
            rel.setConfigId(configId);
            rel.setStandardId(standardId);
            rel.setConfigType(configType);
            Timestamp now = new Timestamp(System.currentTimeMillis());
            rel.setStatus("1");
            rel.setCreatorId(currentUserId);
            rel.setCreatorName(currentUserName);
            rel.setCreateTime(now);
            rel.setModifyierId(currentUserId);
            rel.setModifyierName(currentUserName);
            rel.setModifyTime(now);
            insightConfigStandardRelMapper.insert(rel);
        }
    }

    /**
     * 创建维度配置
     */
    private void createDimensionConfig(InsightDimensionConfigDTO dto, Long agentId) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();
        InsightDimensionConfig dimensionConfig = new InsightDimensionConfig();
        BeanUtils.copyProperties(dto, dimensionConfig);
        dimensionConfig.setAgentId(agentId);

        // 自动生成维度编码和名称（如果前端没有传递）
        if (dimensionConfig.getDimensionCode() == null) {
            // 根据数据项自动推导维度编码
            String dimensionCode = deriveDimensionCodeFromDataItems(dto.getDataSources());
            dimensionConfig.setDimensionCode(dimensionCode);
        }

        if (dimensionConfig.getDimensionName() == null) {
            // 根据维度编码生成维度名称
            String dimensionName = deriveDimensionNameFromCode(dimensionConfig.getDimensionCode());
            dimensionConfig.setDimensionName(dimensionName);
        }

        Timestamp now = new Timestamp(System.currentTimeMillis());
        dimensionConfig.setStatus("1");
        dimensionConfig.setCreatorId(currentUserId);
        dimensionConfig.setCreatorName(currentUserName);
        dimensionConfig.setCreateTime(now);
        dimensionConfig.setModifyierId(currentUserId);
        dimensionConfig.setModifyierName(currentUserName);
        dimensionConfig.setModifyTime(now);
        insightDimensionConfigMapper.insert(dimensionConfig);
        Long newDimensionConfigId = dimensionConfig.getId();

        // 保存结构化提示词片段
        if (dto.getInterpretationPromptFragments() != null && !dto.getInterpretationPromptFragments().isEmpty()) {
            MainPromptDTO mainPromptDTO = new MainPromptDTO();
            mainPromptDTO.setPromptName(dimensionConfig.getDimensionName() + "解读提示词");
            mainPromptDTO.setPromptType("DIMENSION");
            mainPromptDTO.setConfigId(newDimensionConfigId);
            mainPromptDTO.setFragments(dto.getInterpretationPromptFragments());
            promptService.saveMainPrompt(mainPromptDTO);
        }

        saveDimensionDataItemRelations(newDimensionConfigId, dto.getDataSources());
        saveDimensionStandardRelations(newDimensionConfigId, dto.getStandards(), CONFIG_TYPE_DIMENSION);
    }

    /**
     * 更新维度配置
     */
    private void updateDimensionConfig(InsightDimensionConfigDTO dto, Long agentId) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();

        // 先查询现有数据
        InsightDimensionConfig existingConfig = insightDimensionConfigMapper.selectById(dto.getId());
        if (existingConfig == null) {
            throw new RuntimeException("维度配置不存在，ID: " + dto.getId());
        }

        // 只更新非null的字段，保持其他字段不变
        InsightDimensionConfig dimensionConfig = new InsightDimensionConfig();
        dimensionConfig.setId(dto.getId());
        dimensionConfig.setAgentId(agentId);

        // 只更新有值的字段
        if (dto.getDimensionCode() != null) {
            dimensionConfig.setDimensionCode(dto.getDimensionCode());
        }
        if (dto.getDimensionName() != null) {
            dimensionConfig.setDimensionName(dto.getDimensionName());
        }
        if (dto.getInterpretationPrompt() != null) {
            dimensionConfig.setInterpretationPrompt(dto.getInterpretationPrompt());
        }
        if (dto.getSortOrder() != null) {
            dimensionConfig.setSortOrder(dto.getSortOrder());
        }

        // 设置修改相关字段
        dimensionConfig.setModifyierId(currentUserId);
        dimensionConfig.setModifyierName(currentUserName);
        dimensionConfig.setModifyTime(new Timestamp(System.currentTimeMillis()));

        insightDimensionConfigMapper.updateById(dimensionConfig);

        // 更新结构化提示词片段
        if (dto.getInterpretationPromptFragments() != null) {
            MainPromptDTO mainPromptDTO = new MainPromptDTO();
            mainPromptDTO.setPromptName(dimensionConfig.getDimensionName() + "解读提示词");
            mainPromptDTO.setPromptType("DIMENSION");
            mainPromptDTO.setConfigId(dto.getId());
            mainPromptDTO.setFragments(dto.getInterpretationPromptFragments());
            promptService.saveMainPrompt(mainPromptDTO);
        }

        insightDimensionRefItemRelMapper.deleteByDimensionConfigId(dto.getId());
        saveDimensionDataItemRelations(dto.getId(), dto.getDataSources());
        insightConfigStandardRelMapper.deleteByConfigIdsAndType(Collections.singletonList(dto.getId()),
                CONFIG_TYPE_DIMENSION);
        saveDimensionStandardRelations(dto.getId(), dto.getStandards(), CONFIG_TYPE_DIMENSION);
    }

    /**
     * 保存维度数据项关系
     */
    private void saveDimensionDataItemRelations(Long dimensionConfigId, List<InsightDataSourceDTO> dataSources) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();

        if (dataSources != null) {
            int sortOrder = 1;
            for (int i = 0; i < dataSources.size(); i++) {
                InsightDataSourceDTO dataSource = dataSources.get(i);
                if (dataSource.getSelectedDataItemIds() != null) {
                    for (Long dataItemId : dataSource.getSelectedDataItemIds()) {
                        // 通过数据项ID查询数据项信息
                        InsightDataItem dataItem = insightDataItemMapper.selectById(dataItemId);
                        if (dataItem == null) {
                            log.warn("数据项不存在，ID: {}", dataItemId);
                            continue;
                        }

                        // 通过数据项的queryBusinessCode查询对应的数据源
                        List<InsightDataSource> dataSourceList = insightDataSourceMapper
                                .selectBySourceCode(dataItem.getQueryBusinessCode());
                        if (dataSourceList.isEmpty()) {
                            log.warn("数据源不存在，sourceCode: {}", dataItem.getQueryBusinessCode());
                            continue;
                        }

                        InsightDimensionRefItemRel rel = new InsightDimensionRefItemRel();
                        rel.setDimensionConfigId(dimensionConfigId);
                        rel.setRefDataItemId(dataItemId);
                        rel.setSortOrder(sortOrder++);
                        rel.setStatus("1");
                        Timestamp now = new Timestamp(System.currentTimeMillis());
                        rel.setCreatorId(currentUserId);
                        rel.setCreatorName(currentUserName);
                        rel.setCreateTime(now);
                        rel.setModifyierId(currentUserId);
                        rel.setModifyierName(currentUserName);
                        rel.setModifyTime(now);
                        insightDimensionRefItemRelMapper.insert(rel);
                    }
                }
            }
        }
    }

    /**
     * 创建总结配置
     */
    private void createSummaryConfig(InsightSummaryConfigDTO dto, Long agentId) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();
        InsightSummaryConfig summaryConfig = new InsightSummaryConfig();
        BeanUtils.copyProperties(dto, summaryConfig);
        summaryConfig.setAgentId(agentId);
        Timestamp now = new Timestamp(System.currentTimeMillis());
        summaryConfig.setStatus("1");
        summaryConfig.setCreatorId(currentUserId);
        summaryConfig.setCreatorName(currentUserName);
        summaryConfig.setCreateTime(now);
        summaryConfig.setModifyierId(currentUserId);
        summaryConfig.setModifyierName(currentUserName);
        summaryConfig.setModifyTime(now);
        insightSummaryConfigMapper.insert(summaryConfig);
        Long newSummaryConfigId = summaryConfig.getId();

        // 保存综合分析结构化提示词片段
        if (dto.getComprehensivePromptFragments() != null && !dto.getComprehensivePromptFragments().isEmpty()) {
            MainPromptDTO comprehensivePromptDTO = new MainPromptDTO();
            comprehensivePromptDTO.setPromptName("综合分析提示词");
            comprehensivePromptDTO.setPromptType("SUMMARY_COMPREHENSIVE");
            comprehensivePromptDTO.setConfigId(newSummaryConfigId);
            comprehensivePromptDTO.setFragments(dto.getComprehensivePromptFragments());
            promptService.saveMainPrompt(comprehensivePromptDTO);
        }

        // 保存建议结构化提示词片段
        if (dto.getSummaryAdvicePromptFragments() != null && !dto.getSummaryAdvicePromptFragments().isEmpty()) {
            MainPromptDTO advicePromptDTO = new MainPromptDTO();
            advicePromptDTO.setPromptName("建议提示词");
            advicePromptDTO.setPromptType("SUMMARY_ADVICE");
            advicePromptDTO.setConfigId(newSummaryConfigId);
            advicePromptDTO.setFragments(dto.getSummaryAdvicePromptFragments());
            promptService.saveMainPrompt(advicePromptDTO);
        }

        saveDimensionStandardRelations(newSummaryConfigId, dto.getStandards(), CONFIG_TYPE_SUMMARY);
    }

    /**
     * 更新总结配置
     */
    private void updateSummaryConfig(InsightSummaryConfigDTO dto, Long agentId) {
        TenantUser currentUser = UserManager.getTenantUser();
        Long currentUserId = currentUser.getUserId();
        String currentUserName = currentUser.getUserName();

        // 查询现有配置
        InsightSummaryConfig existingConfig = insightSummaryConfigMapper.selectByAgentId(agentId);
        if (existingConfig == null) {
            throw new RuntimeException("总结配置不存在，Agent ID: " + agentId);
        }

        // 更新配置
        InsightSummaryConfig summaryConfig = new InsightSummaryConfig();
        summaryConfig.setId(existingConfig.getId()); // 使用查询到的ID
        summaryConfig.setAgentId(agentId);

        // 只更新有值的字段
        if (dto.getComprehensivePrompt() != null && !dto.getComprehensivePrompt().trim().isEmpty()) {
            summaryConfig.setComprehensivePrompt(dto.getComprehensivePrompt());
        }
        if (dto.getSummaryAdvicePrompt() != null && !dto.getSummaryAdvicePrompt().trim().isEmpty()) {
            summaryConfig.setSummaryAdvicePrompt(dto.getSummaryAdvicePrompt());
        }

        // 设置修改相关字段
        summaryConfig.setModifyierId(currentUserId);
        summaryConfig.setModifyierName(currentUserName);
        summaryConfig.setModifyTime(new Timestamp(System.currentTimeMillis()));

        insightSummaryConfigMapper.updateById(summaryConfig);

        // 更新综合分析结构化提示词片段
        if (dto.getComprehensivePromptFragments() != null) {
            MainPromptDTO comprehensivePromptDTO = new MainPromptDTO();
            comprehensivePromptDTO.setPromptName("综合分析提示词");
            comprehensivePromptDTO.setPromptType("SUMMARY_COMPREHENSIVE");
            comprehensivePromptDTO.setConfigId(existingConfig.getId());
            comprehensivePromptDTO.setFragments(dto.getComprehensivePromptFragments());
            promptService.saveMainPrompt(comprehensivePromptDTO);
        }

        // 更新建议结构化提示词片段
        if (dto.getSummaryAdvicePromptFragments() != null) {
            MainPromptDTO advicePromptDTO = new MainPromptDTO();
            advicePromptDTO.setPromptName("建议提示词");
            advicePromptDTO.setPromptType("SUMMARY_ADVICE");
            advicePromptDTO.setConfigId(existingConfig.getId());
            advicePromptDTO.setFragments(dto.getSummaryAdvicePromptFragments());
            promptService.saveMainPrompt(advicePromptDTO);
        }

        // 更新标准关联
        insightConfigStandardRelMapper.deleteByConfigIdsAndType(
                Collections.singletonList(existingConfig.getId()), CONFIG_TYPE_SUMMARY);
        saveDimensionStandardRelations(existingConfig.getId(), dto.getStandards(), CONFIG_TYPE_SUMMARY);
    }

    private InsightDimensionConfigVO buildDimensionConfigVO(InsightDimensionConfig config) {
        InsightDimensionConfigVO vo = new InsightDimensionConfigVO();
        BeanUtils.copyProperties(config, vo);

        // 查询结构化提示词片段
        MainPromptDTO mainPromptDTO = promptService.getMainPromptByConfigIdAndType(config.getId(), "DIMENSION");
        if (mainPromptDTO != null && mainPromptDTO.getFragments() != null) {
            vo.setInterpretationPromptFragments(mainPromptDTO.getFragments());
        }

        // 数据项现在通过数据源挂载，不再直接关联到维度
        // 查询维度关联的标准
        vo.setStandards(getStandardsForConfig(config.getId(), CONFIG_TYPE_DIMENSION));
        return vo;
    }

    private InsightSummaryConfigVO buildSummaryConfigVO(InsightSummaryConfig config) {
        InsightSummaryConfigVO vo = new InsightSummaryConfigVO();
        BeanUtils.copyProperties(config, vo);
        // 不返回总结配置的ID，因为前端不需要使用
        vo.setId(null);

        // 查询综合分析结构化提示词片段
        MainPromptDTO comprehensivePromptDTO = promptService.getMainPromptByConfigIdAndType(config.getId(),
                "SUMMARY_COMPREHENSIVE");
        if (comprehensivePromptDTO != null && comprehensivePromptDTO.getFragments() != null) {
            vo.setComprehensivePromptFragments(comprehensivePromptDTO.getFragments());
        }

        // 查询建议结构化提示词片段
        MainPromptDTO advicePromptDTO = promptService.getMainPromptByConfigIdAndType(config.getId(), "SUMMARY_ADVICE");
        if (advicePromptDTO != null && advicePromptDTO.getFragments() != null) {
            vo.setSummaryAdvicePromptFragments(advicePromptDTO.getFragments());
        }

        vo.setStandards(getStandardsForConfig(config.getId(), CONFIG_TYPE_SUMMARY));
        return vo;
    }

    private List<InsightStandard> getStandardsForConfig(Long configId, String configType) {
        List<InsightConfigStandardRel> relations = insightConfigStandardRelMapper.selectByConfigIdAndType(configId,
                configType);
        List<Long> standardIds = relations.stream()
                .map(InsightConfigStandardRel::getStandardId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(standardIds)) {
            return insightStandardMapper.selectBatchIds(standardIds);
        }
        return Collections.emptyList();
    }

    /**
     * 标记选中的数据项
     */
    private void markSelectedDataItems(List<InsightDataItem> items, Long selectedItemId) {
        if (items != null) {
            for (InsightDataItem item : items) {
                if (item.getId().equals(selectedItemId)) {
                    item.setSelected(true);
                    break;
                }
                if (item.getChildren() != null) {
                    markSelectedDataItems(item.getChildren(), selectedItemId);
                }
            }
        }
    }

    /**
     * 查找选中的数据项
     */
    private InsightDataItem findSelectedDataItem(List<InsightDataItem> items, Long selectedItemId) {
        if (items != null) {
            for (InsightDataItem item : items) {
                if (item.getId().equals(selectedItemId)) {
                    return item;
                }
                if (item.getChildren() != null) {
                    InsightDataItem foundItem = findSelectedDataItem(item.getChildren(), selectedItemId);
                    if (foundItem != null) {
                        return foundItem;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 创建选中的数据项副本，保持父子关系结构
     */
    private InsightDataItem createSelectedDataItemCopy(InsightDataItem item) {
        InsightDataItem copiedItem = new InsightDataItem();
        BeanUtils.copyProperties(item, copiedItem);

        // 处理子项，只保留被选中的子项
        if (item.getChildren() != null && !item.getChildren().isEmpty()) {
            List<InsightDataItem> selectedChildren = new ArrayList<>();
            for (InsightDataItem child : item.getChildren()) {
                if (child.getSelected() != null && child.getSelected()) {
                    // 递归处理子项的子项
                    InsightDataItem copiedChild = createSelectedDataItemCopy(child);
                    selectedChildren.add(copiedChild);
                }
            }
            copiedItem.setChildren(selectedChildren.isEmpty() ? null : selectedChildren);
        } else {
            copiedItem.setChildren(null);
        }

        return copiedItem;
    }

    /**
     * 根据数据项推导维度编码
     */
    private String deriveDimensionCodeFromDataItems(List<InsightDataSourceDTO> dataSources) {
        if (dataSources == null || dataSources.isEmpty()) {
            return DimensionCode.CUSTOM.getCode();
        }

        // 获取第一个数据项，通过其queryBusinessCode推导维度编码
        for (InsightDataSourceDTO dataSource : dataSources) {
            if (dataSource.getSelectedDataItemIds() != null && !dataSource.getSelectedDataItemIds().isEmpty()) {
                Long firstDataItemId = dataSource.getSelectedDataItemIds().get(0);
                InsightDataItem dataItem = insightDataItemMapper.selectById(firstDataItemId);
                if (dataItem != null && dataItem.getQueryBusinessCode() != null) {
                    // 根据queryBusinessCode推导维度编码
                    return deriveDimensionCodeFromQueryBusinessCode(dataItem.getQueryBusinessCode());
                }
            }
        }

        return DimensionCode.CUSTOM.getCode();
    }

    /**
     * 根据queryBusinessCode推导维度编码
     */
    private String deriveDimensionCodeFromQueryBusinessCode(String queryBusinessCode) {
        if (queryBusinessCode == null) {
            return DimensionCode.CUSTOM.getCode();
        }

        // 使用枚举类的方法推导维度编码
        DimensionCode dimensionCode = DimensionCode.fromQueryBusinessCode(queryBusinessCode);
        return dimensionCode.getCode();
    }

    /**
     * 根据维度编码生成维度名称
     */
    private String deriveDimensionNameFromCode(String dimensionCode) {
        if (dimensionCode == null) {
            return DimensionCode.CUSTOM.getName();
        }

        // 使用枚举类的方法获取维度名称
        DimensionCode code = DimensionCode.fromCode(dimensionCode);
        return code.getName();
    }

    @Override
    public InsightConfigWorkspaceVO.AvailableOptions getAvailableOptions() {
        InsightConfigWorkspaceVO.AvailableOptions options = new InsightConfigWorkspaceVO.AvailableOptions();
        // 按维度分组数据源
        List<InsightDataSource> allDataSources = insightDataSourceMapper.selectAll();
        Map<String, List<InsightDataSource>> dataSourcesByDimension = allDataSources.stream()
                .collect(Collectors.groupingBy(InsightDataSource::getBelongDimensionCode));
        // 构建维度下的数据源结构
        List<InsightDimensionConfigVO> availableDimensionVOs = new ArrayList<>();
        for (Map.Entry<String, List<InsightDataSource>> entry : dataSourcesByDimension.entrySet()) {
            String dimensionCode = entry.getKey();
            List<InsightDataSource> dataSources = entry.getValue();
            InsightDimensionConfigVO dimensionVO = new InsightDimensionConfigVO();
            dimensionVO.setDimensionCode(dimensionCode);
            dimensionVO.setDimensionName(dataSources.get(0).getBelongDimensionName());
            List<InsightDataSourceVO> dataSourceVOs = dataSources.stream().map(dataSource -> {
                InsightDataSourceVO dsVO = new InsightDataSourceVO();
                BeanUtils.copyProperties(dataSource, dsVO);
                List<InsightDataItem> items = insightDataItemMapper
                        .selectByQueryBusinessCode(dataSource.getSourceCode());
                List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);
                Map<String, List<InsightDataItem>> itemsByTypeMap = treeItems.stream()
                        .collect(Collectors.groupingBy(InsightDataItem::getDataTypeCode));
                List<InsightDataItemGroup> itemsByType = itemsByTypeMap.entrySet().stream()
                        .map(mapEntry -> {
                            List<InsightDataItem> copiedItems = mapEntry.getValue().stream()
                                    .map(item -> {
                                        InsightDataItem copiedItem = new InsightDataItem();
                                        BeanUtils.copyProperties(item, copiedItem);
                                        copiedItem.setChildren(null);
                                        return copiedItem;
                                    })
                                    .collect(Collectors.toList());
                            return new InsightDataItemGroup(mapEntry.getKey(), null, copiedItems);
                        })
                        .collect(Collectors.toList());
                dsVO.setDataItemsByType(itemsByType);
                return dsVO;
            }).collect(Collectors.toList());
            dimensionVO.setDataSources(dataSourceVOs);
            availableDimensionVOs.add(dimensionVO);
        }
        options.setDimensions(availableDimensionVOs);
        return options;
    }
}