package com.qc.agent.app.agent.model.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 提示词片段实体类
 */
@Data
public class PromptFragment implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 提示词片段ID，主键
     */
    private Long id;
    
    /**
     * 逻辑删除状态：1-有效，0-无效
     */
    private String status;
    
    /**
     * 关联主提示词ID
     */
    private Long mainPromptId;
    
    /**
     * 片段键名
     */
    private String fragmentKey;
    
    /**
     * 片段值
     */
    private String fragmentValue;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
}