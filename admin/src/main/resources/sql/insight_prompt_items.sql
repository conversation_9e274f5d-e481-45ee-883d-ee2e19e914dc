-- 主提示词表
CREATE TABLE qc_ai_main_prompt (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 提示词基本信息
    prompt_name VARCHAR(100) NOT NULL,           -- 提示词名称
    prompt_type VARCHAR(20) NOT NULL,           -- 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
    
    -- 关联信息
    config_id BIGINT -- 关联qc_ai_summary_config或者qc_ai_dimension_config的id
);
COMMENT ON TABLE qc_ai_main_prompt IS '主提示词表';
COMMENT ON COLUMN qc_ai_main_prompt.id IS '主提示词ID，主键';
COMMENT ON COLUMN qc_ai_main_prompt.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_name IS '提示词名称';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_type IS '提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE';
COMMENT ON COLUMN qc_ai_main_prompt.config_id IS '关联qc_ai_summary_config或者qc_ai_dimension_config的id';

-- 提示词片段表
CREATE TABLE qc_ai_prompt_fragment (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 关联信息
    main_prompt_id BIGINT NOT NULL,             -- 关联主提示词ID
    
    -- 片段信息
    fragment_key VARCHAR(100) NOT NULL,         -- 片段键名
    fragment_value TEXT NOT NULL,               -- 片段值
    sort_order INTEGER DEFAULT 0                -- 排序号
);
COMMENT ON TABLE qc_ai_prompt_fragment IS '提示词片段表';
COMMENT ON COLUMN qc_ai_prompt_fragment.id IS '提示词片段ID，主键';
COMMENT ON COLUMN qc_ai_prompt_fragment.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_fragment.main_prompt_id IS '关联主提示词ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_key IS '片段键名';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_value IS '片段值';
COMMENT ON COLUMN qc_ai_prompt_fragment.sort_order IS '排序号';

-- 创建索引
CREATE INDEX idx_main_prompt_config_id ON qc_ai_main_prompt(config_id);
CREATE INDEX idx_main_prompt_type ON qc_ai_main_prompt(prompt_type);
CREATE INDEX idx_prompt_fragment_main_prompt_id ON qc_ai_prompt_fragment(main_prompt_id);
CREATE INDEX idx_prompt_fragment_sort_order ON qc_ai_prompt_fragment(sort_order); 