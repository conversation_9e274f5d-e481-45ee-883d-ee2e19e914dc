<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightSummaryConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightSummaryConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="agent_id" property="agentId" jdbcType="BIGINT"/>
        <result column="comprehensive_prompt" property="comprehensivePrompt" jdbcType="VARCHAR"/>
        <result column="summary_advice_prompt" property="summaryAdvicePrompt" jdbcType="VARCHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, agent_id, comprehensive_prompt, summary_advice_prompt,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_summary_config
        WHERE id = #{id}
    </select>

    <!-- 根据代理ID查询 -->
    <select id="selectByAgentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_summary_config
        WHERE agent_id = #{agentId}
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_summary_config
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightSummaryConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_summary_config (
            status, agent_id, comprehensive_prompt, summary_advice_prompt,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{agentId}, #{comprehensivePrompt}, #{summaryAdvicePrompt},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightSummaryConfig">
        UPDATE qc_ai_summary_config
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="comprehensivePrompt != null">comprehensive_prompt = #{comprehensivePrompt},</if>
            <if test="summaryAdvicePrompt != null">summary_advice_prompt = #{summaryAdvicePrompt},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_summary_config WHERE id = #{id}
    </delete>

    <!-- 根据代理ID删除 -->
    <delete id="deleteByAgentId" parameterType="java.lang.Long">
        DELETE FROM qc_ai_summary_config WHERE agent_id = #{agentId}
    </delete>

</mapper> 