package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightDimensionConfig;

/**
 * 维度配置表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightDimensionConfigMapper {

    /**
     * 根据ID查询维度配置
     */
    InsightDimensionConfig selectById(@Param("id") Long id);

    /**
     * 根据代理ID查询维度配置列表
     */
    List<InsightDimensionConfig> selectByAgentId(@Param("agentId") Long agentId);

    /**
     * 插入维度配置
     */
    int insert(InsightDimensionConfig dimensionConfig);

    /**
     * 更新维度配置
     */
    int updateById(InsightDimensionConfig dimensionConfig);

    /**
     * 根据ID删除维度配置
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID列表批量删除
     */
    int deleteBatchIds(@Param("ids") List<Long> ids);
}