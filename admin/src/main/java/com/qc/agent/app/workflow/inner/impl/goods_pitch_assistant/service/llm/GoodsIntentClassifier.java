package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsIntentClassifier {

    private final QcAiAgentMapper qcAiAgentMapper;

    private final static String SYSTEM_INTENT_PROMPT = """
            # 任务：意图识别与实体提取
                                       你是一个专业的自然语言理解引擎。你的任务是从用户问题中提取核心意图和相关实体，并严格按照指定的JSON格式输出。

                                       ## 1. 意图定义与边界 (Intent Definitions & Boundaries)
                        
                                       请将用户问题分类到以下意图之一。每个意图都附有详细描述、关键词和判断标准。
                        
                                       *   **`SALES_INTO` (商品卖进)**
                                           *   **描述**:  核心是关于 如何将产品引入或铺货到某个销售渠道 / 具体客户的卖进向策略与动作，包括为实现卖进而需明确的产品卖点、差异化优势等关键策略要素 的问题。
                                           *   **关键词**: "如何卖进", "怎么进入", "铺货", "上架", "打入市场", "有什么卖点", "卖点是什么", "核心优势", "差异化卖点"。
                                           *   **判断**: 提问的重点是 “进入渠道 / 触达客户” 的卖进行动本身，或为支撑该动作所需的策略（如卖点提炼、优势分析等）、方法、咨询。
                        
                                       *   **`LIST_COMPETITORS` (列出竞品)**
                                           *   **描述**: 核心是要求 **列出一个或多个竞争对手或竞品**。
                                           *   **关键词**: "有哪些竞品", "竞争对手是谁", "同类产品", "主要对手"。
                                           *   **判断**: 提问的重点是 **识别并列举一个群体（竞争者）**。
                        
                                       *   **`DIRECT_COMPARISON` (直接对比)**
                                           *   **描述**: 核心是对 **两个或多个明确指定的产品/品牌进行直接的、一对一的比较**。
                                           *   **关键词**: "和...比", "有什么区别", "哪个更好", "优劣势"。
                                           *   **判断**: 提问的重点是对 **两个或多个已确认个体进行详细分析**。
                        
                                       *   **`CUSTOMER_SCHEME` (客户商品信息查询)**
                                           *   **描述**: 核心是查询 某个特定客户 / 门店内，关于特定商品的客户 / 门店专属已有信息 ，如该客户 / 门店的商品价格、价格方案、促销活动、可售商品清单等（不含商品通用属性如供货周期、生产周期等）**。
                                           *   **关键词**: "价格是多少", "有什么促销活动", "XXX门店XXX品牌有哪些可售商品", "XXX门店XX商品当前促销价格是什么", "促销什么时候结束"。
                                           *   **判断**: 提问的重点是 查询已存在的、且特定于某客户 / 门店的事实或数据状态 （如该客户的专属价格、门店的促销活动），需同时满足两个条件：① 明确提及具体客户 / 门店；② 信息类型为客户 / 门店专属（如价格方案、促销）。 若未提及具体客户 / 门店，或查询内容为商品通用属性（如供货周期、材质），则不属于此类 。
                        
                                       *   **`OTHER` (其他)**
                                           *   **描述**: 无法清晰地归入以上任何一类的其他问题。
                        
                                       ### 泛指客户/渠道的排除规则（重点）
                                       > 当用户使用了如下模糊或代指性描述词汇时，不应识别为 `customer` 或 `salesChannel`：
                                       **模糊词示例**：
                                       “该门店”、“这家店”、“商铺”、“这个渠道”、“该客户”、“某超市”、“某平台”、“门市部”、“他们家” 等。
                                       在这些情况下，**只提取产品及品牌，忽略客户或渠道字段。**
                                       #### 示例 1:
                                       - 问题：500G葱伴侣六月香豆瓣酱怎么卖进该门店？
                                       - 正确输出：
                                       ```json
                                       {
                                         "intent": "SALES_INTO",
                                         "realQuestion": "500G葱伴侣六月香豆瓣酱怎么卖进该门店？",
                                         "entities": {
                                           "productName": "六月香豆瓣酱",
                                           "brand": "葱伴侣"
                                         }
                                       }
                                       #### 示例 2:
                                       - 问题：康师傅冰红茶怎么铺货到这家店？
                                       - 正确输出：
                                       ```json
                                       {
                                         "intent": "SALES_INTO",
                                         "realQuestion": "康师傅冰红茶怎么铺货到这家店？",
                                         "entities": {
                                           "productName": "冰红茶",
                                           "brand": "康师傅"
                                         }
                                       }
                                       ### 结构化/连写商品名保留规则
                                       * 若用户提问中包含连续的商品描述字符串（如含下划线、英文、数字、特殊规格、组合词等），无论其是否包含品牌、规格等信息，**应整体作为一个结构化的 `productName` 原样保留，不得拆分。**
                                       * 特征包括但不限于：
                                         - 包含下划线（`_`）或连写结构，如：`百事_美年达_橙味_1.25L`
                                         - 包含明显的数字规格、容量单位，如：`330ML`、`500g`、`1.25L`
                                         - 包含系列词或特殊命名，如：`爆爽版`、`无糖型`、`低钠口味`
                                       * 品牌（`brand`）字段可根据上下文推断，如果不确定则可省略；但结构化商品名不得拆分。
                                       #### 示例 1：
                                       - 问题：百事_美年达_橙味_1.25L怎么推销到运动场？
                                       - 输出：
                                       ```json
                                       {
                                         "intent": "SALES_INTO",
                                         "realQuestion": "百事_美年达_橙味_1.25L怎么推销到运动场？",
                                         "entities": {
                                           "productName": "百事_美年达_橙味_1.25L",
                                           "salesChannel": "运动场"
                                         }
                                       }
                                       #### 示例 2：
                                       - 问题：800G葱伴侣黄豆酱/大众装/自动/1X12怎么推销到食堂？
                                       - 输出：
                                       ```json
                                       {
                                         "intent": "SALES_INTO",
                                         "realQuestion": "800G葱伴侣黄豆酱/大众装/自动/1X12怎么推销到食堂？",
                                         "entities": {
                                           "productName": "800G葱伴侣黄豆酱/大众装/自动/1X12",
                                           "brand": "葱伴侣",
                                           "salesChannel": "食堂"
                                         }
                                       }
                                       ### **意图区分关键点 (Key Differentiation Points)**
                        
                                       *   **`SALES_INTO` vs. `CUSTOMER_SCHEME`**:
                                           *   `SALES_INTO` 是围绕商品的推销、卖进的问题（例如：“如何把A卖进B店？A有什么卖点？如何把A卖进什么渠道”）。
                                           *   `CUSTOMER_SCHEME` 是 **“是什么”** 的数据查询问题（例如：“A在B店的价格是多少？”）。
                                           *   即使问题中提到了具体客户（如“沃尔玛”），但如果核心是“如何卖进去”，意图仍是 `SALES_INTO`。
                        
                                       *   **`LIST_COMPETITORS` vs. `DIRECT_COMPARISON`**:
                                           *   `LIST_COMPETITORS` 是 **“一对多”** 的罗列问题（例如：“可乐的竞品有哪些？”）。
                                           *   `DIRECT_COMPARISON` 是 **“一对一”** 的详细对比问题（例如：“可口可乐和百事可乐的区别？”）。
                        
                                       ## 2. 实体提取规则 (Entity Extraction Rules)
                        
                                       *   `productName`: 商品品类或名称 (如: "可乐", "冰红茶", "雪碧")。
                                       *   `brand`: 品牌名称 (如: "桂花", "康师傅")。当产品名包含明确的品牌和品类时，应尽量拆分。
                                       *   `salesChannel`: 销售渠道的 **类型** (如: "超市", "平台电商", "运动场馆", "校园点", "批发")。是是对各种销售渠道特征的抽象归类（休闲娱乐场所;运动渠道;餐饮渠道;医疗机构;电商零售等）。
                                       *   `customer`: **具体的** 客户或门店名称 (如: "家乐福超市", "沃尔玛", "7-11便利店")。这是一个特指的个体，是销售渠道下的具体执行对象。
                                       *   `productA` / `productB`: 用于 `DIRECT_COMPARISON` 意图，应包含用户提到的完整产品名称，无需拆分品牌。
                        
                                       ## 3. 输出格式与示例 (Output Format & Examples)
                        
                                       严格按照以下JSON结构输出，`realQuestion` 字段为用户原始问题的完整复述。
                        
                                       **示例 1: 产品进驻策略**
                                       *   问题: 如何将百事桂花可乐卖进运动场馆？
                                       *   输出:
                                           {
                                             "intent": "SALES_INTO",
                                             "realQuestion": "如何将百事桂花可乐卖进运动场馆？",
                                             "entities": {
                                               "productName": "桂花可乐",
                                               "brand": "百事",
                                               "salesChannel": "运动场馆"
                                             }
                                           }
                                       *   问题: 500G葱伴侣六月香豆瓣酱怎么卖到掌控加点福？
                                       *   输出:
                                           {
                                             "intent": "SALES_INTO",
                                             "realQuestion": "500G葱伴侣六月香豆瓣酱怎么卖进掌控加点福？",
                                             "entities": {
                                               "productName": "六月香豆瓣酱",
                                               "brand": "葱伴侣",
                                               "customer": "掌控加点福"
                                             }
                                           }
                                       **示例 2: 列出竞品**
                                       *   问题: 桂花可乐有哪些竞品？
                                       *   输出:
                                           {
                                             "intent": "LIST_COMPETITORS",
                                             "realQuestion": "桂花可乐有哪些竞品？",
                                             "entities": {
                                               "productName": "可乐",
                                               "brand": "桂花"
                                             }
                                           }
                                       **示例 3: 直接对比**
                                       *   问题: 百事_可乐型汽水_330ML和元气森林可乐味有什么区别？
                                       *   输出:
                                           {
                                             "intent": "DIRECT_COMPARISON",
                                             "realQuestion": "康师傅冰红茶和统一冰红茶有什么区别？",
                                             "entities": {
                                               "productA": "百事_可乐型汽水_330ML",
                                               "productB": "元气森林可乐味"
                                             }
                                           }
                                       **示例 4: 客户情报查询**
                                       *   问题: 沃尔玛超市雪碧当前促销价格是什么？
                                       *   输出:
                                           {
                                             "intent": "CUSTOMER_SCHEME",
                                             "realQuestion": "沃尔玛超市雪碧当前促销价格是什么？",
                                             "entities": {
                                               "customer": "沃尔玛超市",
                                               "productName": "雪碧"
                                             }
                                           }
                                       **示例 5 (边界情况): 包含具体客户的进驻问题**
                                       *   问题: 我想把我们的矿泉水铺货到华润万家，应该联系谁？
                                       *   输出:
                                           {
                                             "intent": "SALES_INTO",
                                             "realQuestion": "我想把我们的矿泉水铺货到华润万家，应该联系谁？",
                                             "entities": {
                                               "productName": "矿泉水",
                                               "customer": "华润万家"
                                             }
                                           }
                        
            """;

    public BusinessIntent classify(LLAConfig config, LLARequest request) {
        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(6L);
        String systemPrompt = qcAiAgent.getSplitSqlPrompt();
        if (StringUtils.isEmpty(systemPrompt)) {
            systemPrompt = SYSTEM_INTENT_PROMPT;
        }
        LLARequest intentRequest = LLARequest.builder()
                .id(request.getId())
                // 带上轮的会话记录，大模型可以判断意图更准确
                .qas(request.getQas())
                .system(systemPrompt)
                .content(request.getContent())
                .build();

        LLAResponse response = WorkflowLLAClient.send(config, intentRequest, false);
        return parseIntentResult(response);
    }


    public static BusinessIntent parseIntentResult(LLAResponse response) {
        try {
            String responseContent = response.getContent();
            JSONObject result = JSON.parseObject(responseContent);
            String intentCode = result.getString("intent");

            // 添加日志记录
            log.debug("原始LLM响应: {}", responseContent);

            // 容错处理：不存在的意图默认为OTHER
            GoodsIntent intent;
            try {
                intent = GoodsIntent.valueOf(intentCode);
            } catch (IllegalArgumentException e) {
                log.warn("未知意图代码: {}", intentCode);
                intent = GoodsIntent.OTHER;
            }

            // 确保entities总是JSONObject
            JSONObject entities = result.getJSONObject("entities");
            if (entities == null) {
                entities = new JSONObject();
                log.warn("未找到实体对象，使用空对象");
            }

            return new BusinessIntent(intent, entities, response.getUsage());
        } catch (Exception e) {
            log.error("意图解析失败：", e);
            return BusinessIntent.of(GoodsIntent.OTHER);
        }
    }
}
