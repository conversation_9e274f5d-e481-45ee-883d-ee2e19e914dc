package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 洞察配置工作区数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InsightConfigWorkspaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    private Long agentId;

    /**
     * 完整的维度配置列表，以此为准进行全量更新
     */
    private List<InsightDimensionConfigDTO> dimensionConfigurations;

    /**
     * 总结配置，以此为准进行更新
     */
    private InsightSummaryConfigDTO summaryConfiguration;

}