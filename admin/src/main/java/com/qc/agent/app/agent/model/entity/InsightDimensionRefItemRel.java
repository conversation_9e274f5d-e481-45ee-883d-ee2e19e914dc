package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维度配置引用数据项关系表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightDimensionRefItemRel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系 ID，主键
     */
    private Long id;

    /**
     * 维度配置 ID
     */
    private Long dimensionConfigId;

    /**
     * 引用的数据项 ID
     */
    private Long refDataItemId;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 查询参数值 1-最近1个月 2-最近2个月 3-最近3个月
     */
    private String queryValue;

}