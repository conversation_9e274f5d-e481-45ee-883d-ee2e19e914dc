package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.qc.agent.app.agent.model.entity.InsightDataItem;

import lombok.Data;

/**
 * 洞察数据源视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightDataSourceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源 ID
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String sourceName;

    /**
     * 数据源编码
     */
    private String sourceCode;

    /**
     * 关联的数据项列表（按数据类型分组）
     * 数组结构，每个元素包含 dataTypeCode 和对应的数据项列表
     */
    private List<InsightDataItemGroup> dataItemsByType;

    /**
     * 接口 URL
     */
    private String apiUrl;

    /**
     * 请求方式（POST/GET）
     */
    private String httpMethod;

    /**
     * 数据源描述说明
     */
    private String description;

    private String status;
    private String belongDimensionCode;
    private String belongDimensionName;

    /**
     * 查询范围名称（用于前端展示）
     */
    private String queryFieldName;

    /**
     * 查询参数值 1-最近1个月 2-最近2个月 3-最近3个月
     */
    private String queryValue;

    /**
     * 已选择的数据项列表（用于currentDimensionConfiguration）
     * 直接返回选中的数据项，不按类型分组
     */
    private List<InsightDataItem> selectedDataItems;

    private List<InsightDataItem> dataItems;
}