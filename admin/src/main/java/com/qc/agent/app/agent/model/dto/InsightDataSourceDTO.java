package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 数据源数据传输对象（简化版，只包含必要的配置字段）
 * 前端只需要传递查询参数值和选中的数据项ID列表
 * 数据源信息可以通过数据项ID自动推导
 *
 * <AUTHOR>
 */
@Data
public class InsightDataSourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询参数值 1-最近1个月 2-最近2个月 3-最近3个月
     */
    private String queryValue;

    /**
     * 选中的数据项ID列表
     */
    private List<Long> selectedDataItemIds;
}