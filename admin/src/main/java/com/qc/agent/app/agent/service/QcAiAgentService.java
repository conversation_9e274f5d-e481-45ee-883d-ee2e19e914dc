package com.qc.agent.app.agent.service;


import com.qc.agent.app.agent.model.dto.QcAiAgentPublishDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentSaveDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.query.QcAiAgentBaseQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentQuery;
import com.qc.agent.app.agent.model.vo.AgentsClientQueryVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentDetailVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentVO;

import java.util.List;

public interface QcAiAgentService {
    QcAiAgent save(QcAiAgentSaveDTO agent);

    List<QcAiAgentVO> queryCustomAgents(String name, String publishFlag);

    void publish(QcAiAgentPublishDTO publishDTO);

    void deactivate(Long id, Long userId, String userName);

    QcAiAgentDetailVO queryAgentDetail(Long id);

    void enable(Long id, Long userId, String userName);

    void disable(Long id, Long userId, String userName);

    void delete(Long id, Long userId, String userName);

    List<QcAiAgentVO> seekAgents(QcAiAgentQuery query);

    List<QcAiAgentVO> queryInternalAgents(QcAiAgentBaseQuery query);

    AgentsClientQueryVO queryClientAgents(List<Long> deptIds);

    List<QcAiAgentVO> queryAllAgents(QcAiAgentQuery query);

    void authorityDetail(QcAiAgentPublishDTO publishDTO);

    void generateLeadingQuestionsForExistingTenants(Long tenantId);

    boolean showInVisitTask(List<Long> deptIds);

}
