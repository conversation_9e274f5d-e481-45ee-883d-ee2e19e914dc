package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 总结配置表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightSummaryConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总结配置 ID，主键
     */
    private Long id;

    /**
     * 关联的智能体 ID
     */
    private Long agentId;

    /**
     * 综合衡量标准提示词
     */
    private String comprehensivePrompt;

    /**
     * 总结及建议提示词
     */
    private String summaryAdvicePrompt;

}