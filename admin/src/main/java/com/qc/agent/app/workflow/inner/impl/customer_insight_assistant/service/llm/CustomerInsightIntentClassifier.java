package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.llm;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 客户洞察意图分类器
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerInsightIntentClassifier {

    private final QcAiAgentMapper qcAiAgentMapper;

    private static final String SYSTEM_INTENT_PROMPT = """
            # 任务：客户洞察意图识别与实体提取
            你是一个专业的客户洞察分析引擎。你的任务是从用户问题中提取核心意图和相关实体，并严格按照指定的JSON格式输出。

            ## 1. 意图定义与边界 (Intent Definitions & Boundaries)

            请将用户问题分类到以下意图之一。每个意图都附有详细描述、关键词和判断标准。

            *   **`CUSTOMER_ANALYSIS` (客户分析)**
                *   **描述**: 核心是关于客户基本信息、特征、属性等静态信息的分析。
                *   **关键词**: "客户特征", "客户信息", "客户资料", "客户属性", "客户基本情况"。
                *   **判断**: 提问的重点是了解客户的基本信息、特征、属性等静态数据。

            *   **`OTHER` (其他)**
                *   **描述**: 不属于上述意图的其他问题。

            ## 2. 实体提取规则 (Entity Extraction Rules)

            *   `customer`: 客户名称 (如: "沃尔玛", "家乐福", "7-11")。

            ## 3. 输出格式与示例 (Output Format & Examples)

            严格按照以下JSON结构输出，realQuestion 字段为用户原始问题的完整复述。

            示例 1: 客户分析
            问题: 汇盟源国家广告园店的客户洞察
            输出:
            {
              "intent": "CUSTOMER_ANALYSIS",
              "realQuestion": "汇盟源国家广告园店的客户洞察？",
              "entities": {
                "customer": "汇盟源国家广告园店"
              }
            }

            示例 2: 其他
            问题: 你好
            输出:
            {
              "intent": "OTHER",
              "realQuestion": "你好",
              "entities": {}
            }

            ## 4. 注意事项

            1. 如果问题中没有明确提及客户名称，但上下文暗示了特定客户，请提取相应的客户信息。
            2. 如果问题涉及多个维度，优先选择最核心的意图。
            3. 时间范围和分析维度是可选的，只有在问题中明确提及时才提取。
            4. 确保输出的JSON格式完全正确，不要包含额外的文本或解释。
            """;

    /**
     * 分类客户洞察意图
     * 
     * @param question 用户问题
     * @param logPrefix 日志前缀
     * @return 意图分类结果
     */
    public CustomerInsightBusinessIntent classifyIntent(LLARequest request, LLAConfig config, String logPrefix) {
        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(8L);
        String systemPrompt = qcAiAgent.getSplitSqlPrompt();
        
        if (StringUtils.isEmpty(systemPrompt)) {
            systemPrompt = SYSTEM_INTENT_PROMPT;
        }
        LLARequest intentRequest = LLARequest.builder()
                .id(request.getId())
                // 带上轮的会话记录，大模型可以判断意图更准确
                .qas(request.getQas())
                .system(systemPrompt)
                .content(request.getContent())
                .build();

        LLAResponse response = WorkflowLLAClient.send(config, intentRequest, false);
        return parseIntentResult(response);
    }


    public static CustomerInsightBusinessIntent parseIntentResult(LLAResponse response) {
        try {
            String responseContent = response.getContent();
            JSONObject result = JSON.parseObject(responseContent);
            String intentCode = result.getString("intent");

            // 添加日志记录
            log.debug("原始LLM响应: {}", responseContent);

            // 容错处理：不存在的意图默认为OTHER
            CustomerInsightIntent intent;
            try {
                intent = CustomerInsightIntent.valueOf(intentCode);
            } catch (IllegalArgumentException e) {
                log.warn("未知意图代码: {}", intentCode);
                intent = CustomerInsightIntent.OTHER;
            }

            // 确保entities总是JSONObject
            JSONObject entities = result.getJSONObject("entities");
            if (entities == null) {
                entities = new JSONObject();
                log.warn("未找到实体对象，使用空对象");
            }

            return CustomerInsightBusinessIntent.of(intent, entities);
        } catch (Exception e) {
            log.error("意图解析失败：", e);
            return CustomerInsightBusinessIntent.of(CustomerInsightIntent.OTHER, new JSONObject());
        }
    }

} 