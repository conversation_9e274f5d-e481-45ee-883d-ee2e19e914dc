package com.qc.agent.app.agent.model.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;

import lombok.Data;

@Data
public class QcAiAgentSaveDTO {

    private Long id;

    /**
     * 创建/修改人id
     */
    private Long userId;

    /**
     * 创建/修改人名称
     */
    private String userName;

    /**
     * 智能体名称
     */
    private String name;

    /**
     * 智能体logo
     */
    private String logo;

    /**
     * 提示词
     */
    private String prompt;
    /**
     * 业务提示词
     */
    private String bizPrompt;

    /**
     * 知识库id
     */
    private List<Long> knowledgeIds;

    /**
     * 联网搜索
     */
    private String internetSearch;

    /**
     * 上下文检索数量
     */
    private BigDecimal contextSearchAmount;

    /**
     * 描述
     */
    private String description;

    /**
     * 开场白
     */
    private String introduction;

    /**
     * 常见问题
     */
    private String leadingQuestion;

    /**
     * 空结果回复
     */
    private String nullResultAnswer;

    /**
     * 报错回复
     */
    private String errorResultAnswer;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 采样温度
     */
    private BigDecimal modelTemperature;

    /**
     * 核采样的概率阈值
     */
    private BigDecimal modelTopP;

    /**
     * 请求返回的最大 Token 数
     */
    private BigDecimal modelMaxTokens;

    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;

    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;
    /**
     * 考勤助手-将问题拆分为sql的提示词
     */
    private String splitSqlPrompt;

    /**
     * 授权范围 用户信息
     */
    private List<QcAiAgentUserDTO> userList;
    /**
     * 授权范围 部门信息
     */
    private List<QcAiAgentDeptDTO> deptList;

    private List<Long> deptIdList;

    /**
     * 1：基础信息保存
     */
    private String baseSaveFlag;

    /**
     * agent日志中是否展示完整对话内容 1展示 0不展示
     */
    private String showChatLogContentType;

    public static final String BASE_INFO_SAVE = "1";

    public boolean baseSave() {
        return BASE_INFO_SAVE.equals(baseSaveFlag);
    }

    /**
     * 1：发布
     */
    private String publishFlag;

    public static final String SAVE_PUBLISH = "1";

    /**
     * 意图识别是否启用 1：是 0：否
     */
    private String intentIsEnabled;

    private List<QcAiAgentIntentRecognition> intentList;

    public boolean savePublish() {
        return SAVE_PUBLISH.equals(publishFlag);
    }

    /**
     * 参数配置
     */
    private Map<String, String> extConfigs;

    /**
     * 客户洞察维度配置列表
     */
    private List<InsightDimensionConfigDTO> insightDimensionConfigurations;

    /**
     * 客户洞察总结配置
     */
    private InsightSummaryConfigDTO insightSummaryConfiguration;

}
