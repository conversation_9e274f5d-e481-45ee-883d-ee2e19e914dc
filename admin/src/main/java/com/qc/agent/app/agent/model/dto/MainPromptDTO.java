package com.qc.agent.app.agent.model.dto;

import java.util.List;

import lombok.Data;

/**
 * 主提示词DTO
 *
 * <AUTHOR>
 */
@Data
public class MainPromptDTO {

    /**
     * 主提示词ID
     */
    private Long id;

    /**
     * 提示词名称
     */
    private String promptName;

    /**
     * 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
     */
    private String promptType;

    /**
     * 关联配置ID
     */
    private Long configId;

    /**
     * 提示词片段列表
     */
    private List<PromptFragmentDTO> fragments;
}