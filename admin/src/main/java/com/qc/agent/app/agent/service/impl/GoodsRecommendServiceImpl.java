package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.qc.agent.app.agent.mapper.AgentGoodsRecommendMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.dto.QcAiAgentCommonParam;
import com.qc.agent.app.agent.model.po.*;
import com.qc.agent.app.agent.model.query.QcAiAgentBaseQuery;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantRecommendQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentVO;
import com.qc.agent.app.agent.service.GoodsRecommendService;
import com.qc.agent.app.agent.service.goods_intent.GoodsIntentHandler;
import com.qc.agent.app.agent.service.tool.QcAiAgentConversationTool;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.agent.util.ContextUtil;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.llm.GoodsIntentClassifier;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.CustomerTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAQa;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAUsage;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsRecommendServiceImpl implements GoodsRecommendService {
    public static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Resource
    private CustomerTool customerTool;
    private static final String SALES_INTO_TEMPLATE = """
            没问题，为了能更好地推荐商品卖进话术，我需要了解更多信息，请选择或者直接告诉我都行：
            商品名称：%s
            品牌：%s
            卖进渠道：%s
            """;
    // 修正了模板中的文案，使其与意图匹配
    private static final String CUSTOMER_SCHEME_TEMPLATE = """
            好的，为了能为您查询更精准的商品售卖价格策略，请确认以下信息：
            客户名称：%s
            商品名称：%s
            """;
    // 配置可扩展线程池
    private final ThreadPoolExecutor asyncWriteExecutor = new ThreadPoolExecutor(50, 200, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(5000), new ThreadPoolExecutor.AbortPolicy());
    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private QcAiAgentMapper qcAiAgentMapper;

    @Resource
    private AgentGoodsRecommendMapper agentGoodsRecommendMapper;

    @Resource
    private GoodsIntentClassifier intentClassifier;

    @Resource
    private QcAiAgentModelMapper qcAiAgentModelMapper;
    @Resource
    private QcAiAgentConversationMapper qcAiAgentConversationMapper;
    @Resource
    private Map<GoodsIntent, GoodsIntentHandler> goodsIntentHandlerMap;
    @Resource
    private QcAiAgentConversationTool qcAiAgentConversationTool;

    public static List<Product> getTop3ByCreateTime(List<Product> products) {

        PriorityQueue<Product> heap = new PriorityQueue<>(Comparator.comparing(Product::getCreateTimeObj));

        for (Product p : products) {
            if (p.getTagValues() == null || p.getTagValues().trim().isEmpty()) {
                continue;
            }

            try {
                LocalDateTime time = LocalDateTime.parse(p.getCreateTime(), formatter);
                p.setCreateTimeObj(time);
                heap.offer(p);

                if (heap.size() > 3) {
                    heap.poll();
                }
            } catch (Exception ignored) {

            }
        }

        List<Product> result = new ArrayList<>(heap);
        result.sort(Comparator.comparing(Product::getCreateTimeObj).reversed());
        return result;
    }

    @Override
    public JSONObject generateRecommendQuestions(QcAiGoodsAssistantRecommendQuery query) {
        JSONObject result = new JSONObject();
        try {
            QcAiAgentVO qcAiAgent = qcAiAgentMapper.queryInternalAgentById(6L);

            result.put("agent", qcAiAgent);
            //如果storeId为空，返回企业内置问题
            if (query.getStoreId() == null) {
                result.put("questions", getBuiltInQuestions());
                result.put("productList", Collections.emptyList());
                return result;
            }
            Map<String, Long> timers = new LinkedHashMap<>();
            long startTime = System.currentTimeMillis();
            long stepStart;
            //1. 按照优先级查询数据
            stepStart = System.currentTimeMillis();
            PriorityData newPriorityData = queryByPriority(query.getStoreId());
            timers.put("优先级查询耗时", System.currentTimeMillis() - stepStart);

            // 如果优先级查询结果为空，则返回企业内置问题
            if (newPriorityData.getGoodsList().isEmpty()) {
                result.put("questions", getBuiltInQuestions());
                result.put("productList", Collections.emptyList());
                return result;
            }

            stepStart = System.currentTimeMillis();
            List<Product> inSaleGoods = queryOnSaleGoods(query.getStoreId());
            timers.put("可售商品查询耗时", System.currentTimeMillis() - stepStart);

            stepStart = System.currentTimeMillis();

            Set<String> priorityGoodIds = newPriorityData.getGoodsList().stream().map(Product::getId).collect(Collectors.toSet());


            List<Product> unsoldGoods = inSaleGoods.parallelStream().filter(product -> !priorityGoodIds.contains(product.getId())).collect(Collectors.toList());

            if (unsoldGoods.isEmpty()) {
                result.put("questions", getBuiltInQuestions());
                result.put("productList", Collections.emptyList());
                return result;
            }
            timers.put("未卖进商品筛选耗时", System.currentTimeMillis() - stepStart);

            List<String> recommendQuestions;
            List<Product> recommendGoods;

            stepStart = System.currentTimeMillis();
            recommendGoods = getTop3ByCreateTime(unsoldGoods);
            //如果recommendGoods为空，返回企业内置问题
            if (recommendGoods.isEmpty()) {
                result.put("questions", getBuiltInQuestions());
                result.put("productList", Collections.emptyList());
                return result;
            }
            recommendQuestions = generateQuestions(recommendGoods);
            timers.put("筛选商品耗时", System.currentTimeMillis() - stepStart);
            // 4. 更新缓存（含时间戳）
            cacheRecommend(query.getStoreId(), PriorityData.builder().source(newPriorityData.source).timestamp(newPriorityData.timestamp).unsoldGoods(unsoldGoods).recommendGoods(recommendGoods).recommendQuestions(recommendQuestions).build());
            timers.put("总耗时", System.currentTimeMillis() - startTime);
            log.info("商品推荐问题生成耗时统计(ms): {}", timers);
            result.put("questions", recommendQuestions);
            result.put("productList", recommendGoods);
            return result;
        } catch (Exception e) {
            log.error("商品推荐问题生成失败", e);
            result.put("questions", getBuiltInQuestions());
            result.put("productList", Collections.emptyList());
            return result;
        }
    }

    private List<String> getBuiltInQuestions() {
        QcAiAgent agent = qcAiAgentMapper.selectById(6L);

        if (agent == null || StringUtils.isBlank(agent.getLeadingQuestion())) {
            return Collections.singletonList("商品话术助手");
        }
        List<String> questions = JSON.parseArray(agent.getLeadingQuestion(), String.class);
        return questions.isEmpty() ? Collections.singletonList("商品话术助手") : questions;
    }

    private List<String> generateQuestions(List<Product> unsoldGoods) {
        if (unsoldGoods.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> questions = new ArrayList<>();
        Product product = unsoldGoods.get(0);

        String productName = StringUtils.isNotEmpty(product.getShortName()) ? product.getShortName() : product.getName();
        questions.add(String.format("%s有什么卖点？", productName));
        questions.add(String.format("%s有哪些竞品？", productName));
        questions.add(String.format("%s当前有什么促销政策？", productName));

        return questions;
    }

    // 优先级查询逻辑 - 返回商品列表和数据时间戳
    private PriorityData queryByPriority(Long storeId) {
        String timestamps = "";
        String specificInterface = qcAiAgentMapper.selectSalesIntoRecommendInterface();

        // 2. 如果配置了特定接口，只调用该接口
        if (specificInterface != null) {
            log.info("企业[{}]配置了特定接口: {}", storeId, specificInterface);
            QueryResult result = callSpecificInterface(specificInterface, storeId);
            return PriorityData.builder().source(specificInterface).goodsList(result != null ? result.goodsList : Collections.emptyList()).timestamp(result != null ? result.timestamp : "").build();
        }


        List<Product> goodsList = Collections.emptyList();
        String[] sources = {"order", "delivery", "sales", "stock"};
        // 按优先级顺序调用接口
        List<Supplier<QueryResult>> queries = Arrays.asList(() -> queryOrderGoods(storeId), () -> queryDeliveryGoods(storeId), () -> querySalesGoods(storeId), () -> queryStockGoods(storeId));
        // 遍历所有优先级
        String source = null;
        for (int i = 0; i < queries.size(); i++) {
            QueryResult result = queries.get(i).get();
            if (result != null && result.goodsList != null && !result.goodsList.isEmpty()) {
                timestamps = result.timestamp;
                goodsList = result.goodsList;
                source = sources[i];
                break;
            }
        }
        return PriorityData.builder().source(source).goodsList(goodsList).timestamp(timestamps).build();
    }

    private QueryResult callSpecificInterface(String interfaceName, Long storeId) {
        switch (interfaceName) {
            case "1":
                return queryOrderGoods(storeId);
            case "2":
                return queryDeliveryGoods(storeId);
            case "3":
                return querySalesGoods(storeId);
            case "4":
                return queryStockGoods(storeId);
            default:
                log.warn("未知接口配置: {}", interfaceName);
                return null;
        }
    }

    private QueryResult queryOrderGoods(Long storeId) {
        // 查询订单商品
        String url = appsvrDomainUrl + "/app/dms/ai/agent/getOrderProducts.do";
        // 计算日期范围 (近三个月)
        JSONObject requestBodyJson = new JSONObject();
        requestBodyJson.put("customer", storeId);
        try {
            String response = AgentBizDataSupport.postFetchBizData(url, requestBodyJson);
            JSONObject json = JSONObject.parseObject(response);
            if (json != null && "1".equals(json.getString("code"))) {
                List<Product> goods = json.getJSONArray("data").stream().map(obj -> {
                    JSONObject item = (JSONObject) obj;
                    return Product.builder().name(item.getString("productName")).id(item.getString("productId")).createTime(item.getString("createTime")).build();
                }).collect(Collectors.toList());
                if (!goods.isEmpty()) {
                    // 取第一个商品的创建时间
                    String timestamp = goods.get(0).getCreateTime();
                    return new QueryResult(timestamp, goods);
                }
            }
        } catch (Exception e) {
            log.error("订单商品查询失败", e);
        }
        return null;
    }

    private QueryResult queryDeliveryGoods(Long storeId) {
        // 查询铺货商品
        String url = appsvrDomainUrl + "/app/newsku/agent/" + storeId + "/getLatestRecord.do";
        try {
            String response = AgentBizDataSupport.fetchBizData(url, "");

            JSONObject json = JSON.parseObject(response);
            if (json != null && "1".equals(json.getString("code"))) {
                List<Product> goods = json.getJSONArray("data").stream().map(obj -> {
                    JSONObject item = (JSONObject) obj;
                    return Product.builder().name(item.getString("product_name")).id(item.getString("product_id")).submitTime(item.getString("submit_time")).build();
                }).collect(Collectors.toList());
                if (!goods.isEmpty()) {
                    String timestamp = goods.get(0).getSubmitTime();
                    return new QueryResult(timestamp, goods);
                }
            }
        } catch (Exception e) {
            log.error("铺货商品查询失败", e);
        }
        return null;
    }

    private QueryResult querySalesGoods(Long storeId) {
        // 查询销量商品
        String url = appsvrDomainUrl + "/app/std_xlsb_bas/agent/" + storeId + "/getLatestRecord.do";
        try {
            String response = AgentBizDataSupport.fetchBizData(url, "");
            JSONObject json = JSON.parseObject(response);
            if (json != null && "1".equals(json.getString("code"))) {
                List<Product> goods = json.getJSONArray("data").stream().map(obj -> {
                    JSONObject item = (JSONObject) obj;
                    return Product.builder().name(item.getString("product_name")).id(item.getString("product_id")).submitTime(item.getString("submit_time")).build();
                }).collect(Collectors.toList());
                if (!goods.isEmpty()) {
                    String timestamp = goods.get(0).getSubmitTime();
                    return new QueryResult(timestamp, goods);
                }
            }
        } catch (Exception e) {
            log.error("销量商品查询失败", e);
        }
        return null;
    }

    private QueryResult queryStockGoods(Long storeId) {
        // 查询库存商品
        String url = appsvrDomainUrl + "/app/stdstore_order/agent/" + storeId + "/getLatestRecord.do";
        try {
            String response = AgentBizDataSupport.fetchBizData(url, "");
            JSONObject json = JSON.parseObject(response);
            if (json != null && "1".equals(json.getString("code"))) {
                List<Product> goods = json.getJSONArray("data").stream().map(obj -> {
                    JSONObject item = (JSONObject) obj;
                    return Product.builder().name(item.getString("product_name")).id(item.getString("product_id")).submitTime(item.getString("submit_time")).build();
                }).collect(Collectors.toList());
                if (!goods.isEmpty()) {
                    String timestamp = goods.get(0).getSubmitTime();
                    return new QueryResult(timestamp, goods);
                }
            }
        } catch (Exception e) {
            log.error("库存商品查询失败", e);
        }
        return null;
    }


    private List<Product> queryOnSaleGoods(Long storeId) {
        String url = appsvrDomainUrl + "/app/pd/aiagent/queryPdInfos.do";
        JSONObject requestBody = new JSONObject();
        requestBody.put("cmId", storeId);

        try {
            String response = AgentBizDataSupport.postFetchBizData(url, requestBody);
            JSONObject json = JSON.parseObject(response);
            if (json != null && "1".equals(json.getString("code"))) {
                List<Product> goods = json.getJSONArray("data").stream().map(obj -> {
                    JSONObject item = (JSONObject) obj;
                    return Product.builder().name(item.getString("name")).shortName(item.getString("shortName")).id(item.getString("id")).tagValues(item.getString("tagValues")).brand(item.getString("brandName")).createTime(item.getString("createTime")).build();
                }).collect(Collectors.toList());
                return goods;
            }
        } catch (Exception e) {
            log.error("可售商品查询失败", e);
        }
        return Collections.emptyList();
    }

    private void cacheRecommend(Long storeId, PriorityData cache) {
        AgentGoodsRecommend entity = new AgentGoodsRecommend();
        entity.setId(UUIDUtils.getUUID2Long());
        entity.setStoreId(storeId);
        entity.setSource(cache.source);
        entity.setTimestamp(cache.getTimestamp());
        entity.setRecommendQuestionsList(cache.getRecommendQuestions());
        entity.setRecommendGoodsList(cache.getRecommendGoods());
        entity.setUnsoldGoodsList(cache.getUnsoldGoods());

        agentGoodsRecommendMapper.upsert(entity);
        saveUnsoldGoods(storeId, cache.getUnsoldGoods());
    }

    private void saveUnsoldGoods(Long storeId, List<Product> unsoldGoods) {
        if (unsoldGoods == null || unsoldGoods.isEmpty()) return;

        asyncWriteExecutor.submit(ContextUtil.wrapWithContext(() -> {
            try {

                Set<Long> existingIds = agentGoodsRecommendMapper.selectUnsoldIdsByStore(storeId);
                Set<Long> newIds = unsoldGoods.stream().map(p -> Long.valueOf(p.getId())).collect(Collectors.toSet());
                Set<Long> toDelete = existingIds.stream().filter(id -> !newIds.contains(id)).collect(Collectors.toSet());
                Set<Long> toInsertIds = newIds.stream().filter(id -> !existingIds.contains(id)).collect(Collectors.toSet());

                if (!toDelete.isEmpty()) {
                    agentGoodsRecommendMapper.batchDeleteStoreUnsoldRel(new ArrayList<>(toDelete), storeId);
                }
                if (!toInsertIds.isEmpty()) {
                    agentGoodsRecommendMapper.batchInsertStoreUnsoldRel(new ArrayList<>(toInsertIds), storeId);
                }

                List<List<Product>> batches = Lists.partition(unsoldGoods, 500);
                batches.forEach(agentGoodsRecommendMapper::batchInsertOrUpdateUnsold);
            } catch (Exception e) {
                log.error("增量更新失败 store:{}", storeId, e);
            }
        }));
    }

    @Override
    public Map<String, Object> intent(QcAiGoodsAssistantQuery query) {
        final String logPrefix = LLMTools.getLogPrefix(query.getAgentId(), UUID.randomUUID().toString());
        log.info("{} ===== 开始处理商品意图识别请求 =====", logPrefix);

        // 步骤 1: 处理特殊情况，如用户点击 "确定"。使用 orElseGet 避免不必要的后续代码执行。
        return handleConfirmClick(query, logPrefix).orElseGet(() -> {

            // 步骤 2: 加载并验证执行所需的前提条件（Agent, Model）
            Prerequisites prerequisites = loadPrerequisites(query.getAgentId(), logPrefix);

            // 步骤 3: 分类并精炼用户意图
            BusinessIntent finalIntent = classifyAndRefineIntent(query, prerequisites, logPrefix);

            // 步骤 4: 根据最终意图生成回答并持久化会话
            ConversationResult conversationResult = generateAnswerAndPersist(query, finalIntent, logPrefix);

            // 步骤 5: 构建并返回最终响应
            Map<String, Object> response = buildResponse(finalIntent, conversationResult, logPrefix);
            log.info("{} ===== 商品意图识别请求处理完成 =====", logPrefix);
            return response;
        });
    }

    /**
     * 处理用户点击"确定"按钮的快捷路径。
     * 如果用户输入是"确定"，则更新上一个会话并返回一个“其他”意图的响应。
     *
     * @param query     用户查询
     * @param logPrefix 日志前缀
     * @return 如果处理了点击事件，则返回响应Map；否则返回空的Optional
     */
    private Optional<Map<String, Object>> handleConfirmClick(QcAiGoodsAssistantQuery query, String logPrefix) {
        if (!"确定".equals(query.getQuestion())) {
            return Optional.empty();
        }

        log.info("{} 检测到用户点击'确定'，执行快捷更新流程。", logPrefix);
        Optional.ofNullable(query.getCommonParam()).ifPresent(commonParam -> updatePreviousConversation(query.getConversationId(), commonParam, logPrefix));

        // 使用Map.of创建不可变Map，更安全简洁
        Map<String, Object> response = Map.of("goodsIntent", Map.of("type", GoodsIntent.OTHER, 
        "entities",   new JSONObject()), 
        "isDealer", customerTool.queryClientData(logPrefix), 
        "conversationId", String.valueOf(query.getConversationId()) // 确保类型一致
        );
        log.info("{} '确定'点击处理完成，返回其他意图。", logPrefix);
        return Optional.of(response);
    }

    /**
     * 根据上一轮的意图，更新会话的Question和Answer。
     * 使用Java 17的Switch表达式进行优化。
     */
    private void updatePreviousConversation(Long conversationId, QcAiAgentCommonParam commonParam, String logPrefix) {
        // 使用Optional链式调用，代码更安全、流畅
        Optional.ofNullable(commonParam.getGoodsIntent()).ifPresent(goodsIntent -> {
            JSONObject entities = goodsIntent.entities();

            // 使用Switch表达式，代码更简洁且具有类型安全性
            String answer = switch (goodsIntent.type()) {
                case SALES_INTO_INPUT ->
                        formatAnswer(SALES_INTO_TEMPLATE, entities, "productName", "brand", "salesChannel");
                case CUSTOMER_SCHEME_INPUT ->
                        formatAnswer(CUSTOMER_SCHEME_TEMPLATE, entities, "customer", "productName");
                default -> {
                    log.warn("{} 在'确定'流程中遇到未处理的意图类型: {}", logPrefix, goodsIntent.type());
                    yield ""; // Switch表达式需要yield返回值
                }
            };

            // 只有在生成了有效答案时才更新，使用StringUtils.hasText更严谨
            if (org.springframework.util.StringUtils.hasText(answer)) {
                QcAiAgentConversation conversation = new QcAiAgentConversation();
                conversation.setId(conversationId);
                conversation.setAnswer(answer);
                qcAiAgentConversationTool.updateConversation(conversation, logPrefix);
                log.info("{} 已更新会话ID: {}", logPrefix, conversationId);
            }
        });
    }

    /**
     * 加载并验证Agent和Model是否存在。
     * 建议: 考虑为 "Not Found" 情况创建自定义的、更具体的业务异常。
     */
    private Prerequisites loadPrerequisites(Long agentId, String logPrefix) {
        log.debug("{} 正在加载Agent和Model...", logPrefix);
        QcAiAgent qcAiAgent = Optional.ofNullable(qcAiAgentMapper.selectById(agentId)).orElseThrow(() -> {
            log.error("{} 未能找到ID为: {} 的智能体，处理中断。", logPrefix, agentId);
            return new IllegalArgumentException("Agent not found with id: " + agentId);
        });

        QcAiAgentModel qcAiAgentModel = Optional.ofNullable(qcAiAgentModelMapper.selectById(qcAiAgent.getModelId())).orElseThrow(() -> {
            log.error("{} 未能找到ID为: {} 的模型，处理中断。", logPrefix, qcAiAgent.getModelId());
            return new IllegalStateException("Model not found for agent with id: " + agentId);
        });

        log.debug("{} Agent和Model加载成功。", logPrefix);
        return new Prerequisites(qcAiAgent, qcAiAgentModel);
    }

    /**
     * 调用LLM进行初步意图分类，并使用特定处理器进行精炼。
     * (此方法已使用Optional，非常现代化，保持不变)
     */
    private BusinessIntent classifyAndRefineIntent(QcAiGoodsAssistantQuery query, Prerequisites prerequisites, String logPrefix) {
        LLAConfig config = LLMTools.initLLAConfig(prerequisites.model());
        LLARequest request = initLLARequest(query);

        log.info("{} 向LLM发送初步意图分类请求...", logPrefix);
        BusinessIntent initialIntent = intentClassifier.classify(config, request);
        log.info("{} LLM初步分类意图为: '{}', 实体: {}", logPrefix, initialIntent.type(), initialIntent.entities());

        return Optional.ofNullable(goodsIntentHandlerMap.get(initialIntent.type())).map(handler -> {
            log.info("{} 找到意图 '{}' 的特定处理器, 开始精炼...", logPrefix, initialIntent.type());
            BusinessIntent handledIntent = handler.handle(initialIntent, query, logPrefix);
            log.info("{} 处理器精炼后的意图为: '{}', 实体: {}", logPrefix, handledIntent.type(), handledIntent.entities());
            return handledIntent;
        }).orElse(initialIntent);
    }

    /**
     * 根据最终意图生成回答，并持久化会话。
     */
    private ConversationResult generateAnswerAndPersist(QcAiGoodsAssistantQuery query, BusinessIntent finalIntent, String logPrefix) {
        QcAiAgentConversation conversation = qcAiAgentConversationTool.assembleConversation(query, logPrefix);
        JSONObject entities = finalIntent.entities();

        // 再次使用Switch表达式
        String answer = switch (finalIntent.type()) {
            case SALES_INTO_INPUT ->
                    formatAnswer(SALES_INTO_TEMPLATE, entities, "productName", "brand", "salesChannel");
            case CUSTOMER_SCHEME_INPUT -> formatAnswer(CUSTOMER_SCHEME_TEMPLATE, entities, "customer", "productName");
            default -> {
                log.info("{} 意图类型 '{}' 无需生成预设回答，将由大模型直接应答。", logPrefix, finalIntent.type());
                yield "";
            }
        };


        LLAUsage usage = finalIntent.usage();
        conversation.setQuestionToken(BigDecimal.valueOf(usage.getPromptTokens()));
        conversation.setAnswerToken(BigDecimal.valueOf(usage.getCompletionTokens()));

        conversation.setAnswer(answer);
        if (org.springframework.util.StringUtils.hasText(answer)) {
            log.info("{} 意图已处理，正在生成应答并保存会话。", logPrefix);
            conversation.setConversationStatus(QcAiAgentConversation.CONVERSATION_SUCCESS);
            conversation.setAnswerTime(LocalDateTime.now());
        }

        // 无论如何都插入会话记录
        qcAiAgentConversationTool.insertConversation(conversation, logPrefix);
        String conversationId = String.valueOf(conversation.getId());
        log.info("{} 会话保存成功，ID: {}", logPrefix, conversationId);

        return new ConversationResult(answer, conversationId);
    }

    /**
     * 构建最终返回给前端的Map。
     */
    private Map<String, Object> buildResponse(BusinessIntent finalIntent, ConversationResult conversationResult, String logPrefix) {
        // 使用Map.of创建不可变Map，更安全
        Map<String, Object> response = Map.of("goodsIntent",
         Map.of("type", finalIntent.type(),
          "entities", finalIntent.entities()), 
         "isDealer", customerTool.queryClientData(logPrefix), 
         "conversationId", conversationResult.conversationId());

        log.debug("{} 构建最终响应: {}", logPrefix, response);
        return response;
    }

    /**
     * 使用模板安全地格式化应答字符串。(此方法本身已经很好了，保持不变)
     */
    private String formatAnswer(String template, JSONObject entities, String... entityKeys) {
        Object[] values = new Object[entityKeys.length];
        for (int i = 0; i < entityKeys.length; i++) {
            values[i] = entities.getOrDefault(entityKeys[i], ""); // 安全获取
        }
        return String.format(template, values);
    }



    private LLARequest initLLARequest(QcAiGoodsAssistantQuery query) {
        List<ConversationVO> conversationVOS = new ArrayList<>();
        if (!Objects.equals(query.getIsNew(), "1")) {
            conversationVOS = qcAiAgentConversationMapper.queryAgentConversation(query.getAgentId(), UserManager.getTenantUser().getUserId(), 1, 0, "1");
        }
        return LLARequest.builder().sessionId(query.getSessionId()).content(query.getQuestion()).qas(Optional.ofNullable(conversationVOS).orElse(Lists.newArrayList()).stream().map(item -> LLAQa.builder().question(item.getQuestion()).answer(item.getAnswer()).build()).collect(Collectors.toList())).build();
    }

    private static class QueryResult {
        String timestamp;
        List<Product> goodsList;

        QueryResult(String timestamp, List<Product> goodsList) {
            this.timestamp = timestamp;
            this.goodsList = goodsList;
        }
    }

    /**
     * 定义一个Record来封装Agent和Model，作为不可变数据载体。(此设计已很好)
     */
    private record Prerequisites(QcAiAgent agent, QcAiAgentModel model) {
    }

    /**
     * 用于封装答案和会话ID的结果类。(此设计已很好)
     */
    private record ConversationResult(String answer, String conversationId) {
    }

    // 定义优先级查询结果类
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class PriorityData {
        /**
         * 优先级  "order", "delivery", "sales", "stock"
         */
        String source;
        /**
         * 查询结果商品列表
         */
        List<Product> goodsList;
        /**
         * 查询结果时间戳 时间戳+优先级有改变则认为缓存失效
         */
        private String timestamp;
        /**
         * 推荐商品问题列表
         */
        private List<String> recommendQuestions;
        /**
         * 推荐商品列表
         */
        private List<Product> recommendGoods;
        /**
         * 未卖进的商品列表
         */
        private List<Product> unsoldGoods;
    }

}
