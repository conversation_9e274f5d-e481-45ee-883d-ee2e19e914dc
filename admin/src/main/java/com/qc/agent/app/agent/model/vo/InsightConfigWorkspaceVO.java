package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 洞察配置工作区视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightConfigWorkspaceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前已保存的维度配置列表
     */
    private List<InsightDimensionConfigVO> currentDimensionConfiguration;

    /**
     * 当前已保存的总结配置
     */
    private InsightSummaryConfigVO currentSummaryConfiguration;

    /**
     * 可用的配置选项
     */
    private AvailableOptions availableOptions;

    @Data
    public static class AvailableOptions implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 所有可用的维度及其包含的数据源列表
         */
        private List<InsightDimensionConfigVO> dimensions;
    }
}