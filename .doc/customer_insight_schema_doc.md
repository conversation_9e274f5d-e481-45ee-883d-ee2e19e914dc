# 客户洞察助手系统数据库表结构说明

---

## 一、数据库概况

- **数据库类型**：PostgreSQL
- **表数量**：8（含核心业务表及扩展表）
- **主要模块分类**：
  - 维度与配置管理
  - 数据源与数据项管理
  - 衡量标准与总结配置
  - 洞察日志与扩展
  - 业务数据记录

---

## 二、表结构详细说明

### 1. `qc_ai_dimension_config`（维度配置表）

| 字段名                | 类型         | 主键 | 外键 | 索引 | 说明                             |
| --------------------- | ------------ | ---- | ---- | ---- | -------------------------------- |
| id                    | BIGSERIAL    | PK   |      | Y    | 维度配置 ID，主键                |
| status                | CHAR(1)      |      |      |      | 状态：1-有效，0-无效             |
| creator_id            | BIGINT       |      |      |      | 创建人 ID                        |
| creator_name          | VARCHAR(100) |      |      |      | 创建人姓名                       |
| create_time           | TIMESTAMP    |      |      |      | 创建时间                         |
| modifyier_id          | BIGINT       |      |      |      | 修改人 ID                        |
| modifyier_name        | VARCHAR(100) |      |      |      | 修改人姓名                       |
| modify_time           | TIMESTAMP    |      |      |      | 修改时间                         |
| agent_id              | BIGINT       |      | Y    |      | 关联的智能体 ID                  |
| dimension_code        | VARCHAR(20)  |      |      |      | 维度编码（如 ORDER、DISPLAY 等） |
| dimension_name        | VARCHAR(100) |      |      |      | 维度展示名称                     |
| interpretation_prompt | TEXT         |      |      |      | 大模型解读提示词                 |
| sort_order            | INTEGER      |      |      |      | 排序顺序                         |

> **用途**：定义智能体的各种分析维度及其配置。

> **业务定位与流程关联**：
>
> - 该表是洞察配置的核心，决定了客户/经销商洞察分析的维度类型和顺序。
> - 业务流程中，洞察数据生成、前端页面配置、AI 提示词生成等均以此表为入口。
> - 与`qc_ai_dimension_ref_data_item_rel`、`qc_ai_config_standard_rel`等表形成维度-数据项-标准的配置链路。

**实际数据示例**：

```sql
-- 维度编码说明
ORDER: 订单维度
DISPLAY: 铺货维度
VIVID: 生动化维度
PAID_DISPLAY: 付费陈列维度
COUPON: 兑换券维度
ASSET: 资产维度
AUTHENTICITY: 真实性维度
CUSTOM: 自定义维度
```

---

### 2. `qc_ai_data_source`（数据源表）

| 字段名                 | 类型         | 主键 | 外键 | 索引 | 说明                 |
| ---------------------- | ------------ | ---- | ---- | ---- | -------------------- |
| id                     | BIGSERIAL    | PK   |      | Y    | 数据源 ID，主键      |
| status                 | CHAR(1)      |      |      |      | 状态：1-有效，0-无效 |
| creator_id             | BIGINT       |      |      |      | 创建人 ID            |
| creator_name           | VARCHAR(100) |      |      |      | 创建人姓名           |
| create_time            | TIMESTAMP    |      |      |      | 创建时间             |
| modifyier_id           | BIGINT       |      |      |      | 修改人 ID            |
| modifyier_name         | VARCHAR(100) |      |      |      | 修改人姓名           |
| modify_time            | TIMESTAMP    |      |      |      | 修改时间             |
| source_name            | VARCHAR(100) |      |      |      | 数据源名称           |
| source_code            | VARCHAR(50)  |      |      | Y    | 数据源编码，全局唯一 |
| api_url                | VARCHAR(255) |      |      |      | 接口 URL             |
| http_method            | VARCHAR(10)  |      |      |      | 请求方式（POST/GET） |
| description            | TEXT         |      |      |      | 数据源描述说明       |
| belong_dimension_code  | VARCHAR(50)  |      |      |      | 数据源从属维度编码   |
| belong_dimension_name  | VARCHAR(100) |      |      |      | 数据源从属维度名称   |
| query_field_name       | VARCHAR(100) |      |      |      | 查询范围名称         |
| query_time_start_field | VARCHAR(100) |      |      |      | 查询范围开始字段     |
| query_time_end_field   | VARCHAR(100) |      |      |      | 查询范围结束字段     |

> **用途**：定义可用的接口和数据来源。

> **业务定位与流程关联**：
>
> - 该表维护所有可供洞察分析调用的外部/内部接口。
> - 洞察数据拉取、数据项配置、接口调用日志等均依赖此表。
> - 与`qc_ai_data_source_ref_data_item`形成数据源-数据项的映射。

**实际数据示例**：

```sql
-- 数据源编码说明
distributionOrder: 分销订单
stockReport: 铺货上报
rephotograph: 翻拍
crossPhotograph: 窜拍
stockOld: 铺货上报（old）
```

---

### 3. `qc_ai_data_source_ref_data_item`（数据源引用数据项定义表）

| 字段名              | 类型         | 主键 | 外键 | 索引 | 说明                          |
| ------------------- | ------------ | ---- | ---- | ---- | ----------------------------- |
| id                  | BIGSERIAL    | PK   |      | Y    | 数据项 ID，主键               |
| status              | CHAR(1)      |      |      |      | 状态：1-有效，0-无效          |
| item_code           | VARCHAR(50)  |      |      | Y    | 数据项编码，全局唯一          |
| item_name           | VARCHAR(100) |      |      |      | 数据项显示名称                |
| query_business_code | VARCHAR(50)  |      |      |      | 关联的查询业务                |
| data_type_code      | VARCHAR(10)  |      |      |      | 数据类型编码（DETAIL/METRIC） |
| placeholder_name    | VARCHAR(50)  |      |      |      | 在提示词中的占位符            |
| description         | TEXT         |      |      |      | 数据项描述说明                |
| sort_order          | INTEGER      |      |      |      | 排序顺序                      |

> **用途**：定义数据源引用的数据项。

> **业务定位与流程关联**：
>
> - 该表定义所有可被洞察维度、数据源引用的原子数据项。
> - 洞察分析时，数据项作为 AI 分析的输入变量，参与提示词拼装和结果展示。
> - 与`qc_ai_dimension_ref_data_item_rel`等表形成数据项的多维引用。

**实际数据示例**：

```sql
-- 数据项编码说明
distributionOrderSatisfactionRate: 订单满足率 (METRIC)
distributionOrderAllocationRate: 订单配货率 (METRIC)
distributionOrderDetailFields: 分销订单明细 (DETAIL)
distributionOrderDetailFields.field1: 分销订单明细-选择字段1 (DETAIL)
distributionOrderDetailFields.field2: 分销订单明细-选择字段2 (DETAIL)
```

---

### 4. `qc_ai_dimension_ref_data_item_rel`（维度数据项关系表）

| 字段名              | 类型         | 主键 | 外键 | 索引 | 说明                                                |
| ------------------- | ------------ | ---- | ---- | ---- | --------------------------------------------------- |
| id                  | BIGSERIAL    | PK   |      | Y    | 关系 ID，主键                                       |
| status              | CHAR(1)      |      |      |      | 状态：1-有效，0-无效                                |
| creator_id          | BIGINT       |      |      |      | 创建人 ID                                           |
| creator_name        | VARCHAR(100) |      |      |      | 创建人姓名                                          |
| create_time         | TIMESTAMP    |      |      |      | 创建时间                                            |
| modifyier_id        | BIGINT       |      |      |      | 修改人 ID                                           |
| modifyier_name      | VARCHAR(100) |      |      |      | 修改人姓名                                          |
| modify_time         | TIMESTAMP    |      |      |      | 修改时间                                            |
| dimension_config_id | BIGINT       |      | Y    | Y    | 维度配置 ID，关联 qc_ai_dimension_config            |
| ref_data_item_id    | BIGINT       |      | Y    | Y    | 引用数据项 ID，关联 qc_ai_data_source_ref_data_item |
| sort_order          | INTEGER      |      |      |      | 排序顺序                                            |
| query_value         | VARCHAR(100) |      |      |      | 查询参数值                                          |

> **用途**：定义维度使用哪些引用数据源及查询参数。

> **业务定位与流程关联**：
>
> - 该表是维度与数据项的桥梁，决定每个维度实际分析哪些数据项。
> - 洞察分析时，AI 根据此表配置动态组装分析内容。
> - 支持不同维度下的数据项定制和查询范围灵活配置。

---

### 5. `qc_ai_measurement_standard`（衡量标准表）

| 字段名              | 类型         | 主键 | 外键 | 索引 | 说明                              |
| ------------------- | ------------ | ---- | ---- | ---- | --------------------------------- |
| id                  | BIGSERIAL    | PK   |      | Y    | 标准 ID，主键                     |
| status              | CHAR(1)      |      |      |      | 状态：1-有效，0-无效              |
| creator_id          | BIGINT       |      |      |      | 创建人 ID                         |
| creator_name        | VARCHAR(100) |      |      |      | 创建人姓名                        |
| create_time         | TIMESTAMP    |      |      |      | 创建时间                          |
| modifyier_id        | BIGINT       |      |      |      | 修改人 ID                         |
| modifyier_name      | VARCHAR(100) |      |      |      | 修改人姓名                        |
| modify_time         | TIMESTAMP    |      |      |      | 修改时间                          |
| standard_name       | VARCHAR(100) |      |      |      | 标准名称                          |
| standard_definition | TEXT         |      |      |      | 标准定义                          |
| standard_type_code  | VARCHAR(10)  |      |      |      | 标准类型编码（DIMENSION/SUMMARY） |
| standard_type       | VARCHAR(20)  |      |      |      | 标准类型中文名称                  |
| sort_order          | INTEGER      |      |      |      | 排序顺序                          |

> **用途**：定义评价标准。

> **业务定位与流程关联**：
>
> - 该表统一维护所有维度和总结的衡量标准。
> - 洞察分析、前端展示、AI 评分等均以此表为标准依据。
> - 与`qc_ai_config_standard_rel`形成多对多配置，支持灵活扩展。

---

### 6. `qc_ai_summary_config`（总结配置表）

| 字段名                | 类型         | 主键 | 外键 | 索引 | 说明                 |
| --------------------- | ------------ | ---- | ---- | ---- | -------------------- |
| id                    | BIGSERIAL    | PK   |      | Y    | 总结配置 ID，主键    |
| status                | CHAR(1)      |      |      |      | 状态：1-有效，0-无效 |
| creator_id            | BIGINT       |      |      |      | 创建人 ID            |
| creator_name          | VARCHAR(100) |      |      |      | 创建人姓名           |
| create_time           | TIMESTAMP    |      |      |      | 创建时间             |
| modifyier_id          | BIGINT       |      |      |      | 修改人 ID            |
| modifyier_name        | VARCHAR(100) |      |      |      | 修改人姓名           |
| modify_time           | TIMESTAMP    |      |      |      | 修改时间             |
| agent_id              | BIGINT       |      | Y    |      | 关联的智能体 ID      |
| comprehensive_prompt  | TEXT         |      |      |      | 综合衡量标准提示词   |
| summary_advice_prompt | TEXT         |      |      |      | 总结及建议提示词     |

> **用途**：定义综合分析和建议生成规则。

> **业务定位与流程关联**：
>
> - 该表配置洞察报告的综合分析和建议生成逻辑。
> - 洞察流程最后一步，AI 根据此表提示词生成最终建议。
> - 与`qc_ai_config_standard_rel`、`qc_ai_measurement_standard`形成总结-标准-配置链路。

---

### 7. `qc_ai_config_standard_rel`（配置与衡量标准关联表）

| 字段名         | 类型         | 主键 | 外键 | 索引 | 说明                                                   |
| -------------- | ------------ | ---- | ---- | ---- | ------------------------------------------------------ |
| id             | BIGSERIAL    | PK   |      | Y    | 关系 ID，主键                                          |
| status         | CHAR(1)      |      |      |      | 状态：1-有效，0-无效                                   |
| creator_id     | BIGINT       |      |      |      | 创建人 ID                                              |
| creator_name   | VARCHAR(100) |      |      |      | 创建人姓名                                             |
| create_time    | TIMESTAMP    |      |      |      | 创建时间                                               |
| modifyier_id   | BIGINT       |      |      |      | 修改人 ID                                              |
| modifyier_name | VARCHAR(100) |      |      |      | 修改人姓名                                             |
| modify_time    | TIMESTAMP    |      |      |      | 修改时间                                               |
| config_id      | BIGINT       |      | Y    | Y    | 配置 ID（qc_ai_summary_config 或 dimension_config_id） |
| config_type    | VARCHAR(10)  |      |      |      | 标准所属分配编码（DIMENSION/SUMMARY）                  |
| standard_id    | BIGINT       |      | Y    | Y    | 标准 ID，关联 qc_ai_measurement_standard               |

> **用途**：配置与衡量标准的多对多统一关联。

> **业务定位与流程关联**：
>
> - 该表实现维度/总结与衡量标准的灵活绑定。
> - 洞察分析、前端配置、AI 评分等均通过此表查找标准。

---

### 8. `qc_ai_conversation_ext`（对话记录扩展表）

| 字段名                   | 类型         | 主键 | 外键 | 索引 | 说明                                   |
| ------------------------ | ------------ | ---- | ---- | ---- | -------------------------------------- |
| id                       | BIGSERIAL    | PK   |      | Y    | 扩展记录 ID，主键                      |
| status                   | CHAR(1)      |      |      |      | 状态：1-有效，0-无效                   |
| conversation_id          | BIGINT       |      | Y    | Y    | 对话 ID，关联 qc_ai_agent_conversation |
| insight_object_id        | BIGINT       |      |      |      | 洞察对象 ID，客户或经销商 ID           |
| insight_object_name      | VARCHAR(200) |      |      |      | 洞察对象名称                           |
| diagnosis_conclusion     | TEXT         |      |      |      | 诊断结论                               |
| order_insight_evaluation | TEXT         |      |      |      | 订单洞察评价                           |
| order_insight_result     | TEXT         |      |      |      | 订单洞察结果                           |
| display_evaluation       | TEXT         |      |      |      | 铺货评价                               |
| display_insight_result   | TEXT         |      |      |      | 铺货洞察结果                           |
| summary_advice           | TEXT         |      |      |      | 总结及建议                             |

> **用途**：存储洞察分析的详细结果和日志。

> **业务定位与流程关联**：
>
> - 该表扩展主对话表，记录每次洞察分析的详细结构化结果。
> - 洞察日志、用户反馈、AI 结果追溯等均以此表为入口。
> - 支持后续洞察优化、模型评估、用户行为分析等。

---

### 9. `qc_ai_insight_data_source_record`（门店洞察数据源记录表）

| 字段名                         | 类型         | 主键 | 外键 | 索引 | 说明                 |
| ------------------------------ | ------------ | ---- | ---- | ---- | -------------------- |
| id                             | BIGSERIAL    | PK   |      | Y    | 记录 ID，主键        |
| status                         | CHAR(1)      |      |      |      | 状态：1-有效，0-无效 |
| create_time                    | TIMESTAMP    |      |      | Y    | 创建时间             |
| customer_id                    | BIGINT       |      |      | Y    | 客户 ID              |
| customer_name                  | VARCHAR(100) |      |      |      | 客户名称             |
| dimension_ref_data_item_rel_id | BIGINT       |      | Y    | Y    | 维度数据项关系表 ID  |
| value                          | TEXT         |      |      |      | 引用数据源结果       |

> **用途**：留存各维度配置的各查询业务中最后得到的引用数据源结果。

> **业务定位与流程关联**：
>
> - 该表是洞察分析的原始数据快照，记录每次分析的底层数据。
> - 洞察结果回溯、数据分区归档、性能优化等均依赖此表。
> - 建议按`create_time`分区，便于高并发场景下的查询和归档。

---

## 三、实体关系说明（ER 图）

```mermaid
erDiagram
    qc_ai_dimension_config ||--o{ qc_ai_dimension_ref_data_item_rel : "1对多"
    qc_ai_data_source ||--o{ qc_ai_data_source_ref_data_item : "1对多"
    qc_ai_dimension_ref_data_item_rel }o--|| qc_ai_data_source_ref_data_item : "多对1"
    qc_ai_dimension_ref_data_item_rel }o--|| qc_ai_dimension_config : "多对1"
    qc_ai_measurement_standard ||--o{ qc_ai_config_standard_rel : "1对多"
    qc_ai_summary_config ||--o{ qc_ai_config_standard_rel : "1对多"
    qc_ai_dimension_config ||--o{ qc_ai_config_standard_rel : "1对多"
    qc_ai_conversation_ext }o--|| qc_ai_agent_conversation : "多对1"
    qc_ai_insight_data_source_record }o--|| qc_ai_dimension_ref_data_item_rel : "多对1"
```

**文字关系描述：**

- 一个维度配置（qc_ai_dimension_config）可以有多个维度数据项关系（qc_ai_dimension_ref_data_item_rel）。
- 一个数据源（qc_ai_data_source）可以关联多个数据项（qc_ai_data_source_ref_data_item）。
- 维度数据项关系表（qc_ai_dimension_ref_data_item_rel）关联维度配置和数据项。
- 衡量标准（qc_ai_measurement_standard）可被多个配置（qc_ai_config_standard_rel）引用。
- 总结配置（qc_ai_summary_config）和维度配置均可通过 qc_ai_config_standard_rel 关联衡量标准。
- 对话扩展表（qc_ai_conversation_ext）扩展了对话主表（qc_ai_agent_conversation）。
- 洞察数据源记录表（qc_ai_insight_data_source_record）记录每个维度数据项的结果。

---

## 四、基础数据插入 SQL 示例

### 1. 插入维度配置示例

```sql
INSERT INTO qc_ai_dimension_config (
    status, creator_id, creator_name, create_time, agent_id, dimension_code, dimension_name, interpretation_prompt, sort_order
) VALUES
('1', 1, '系统管理员', CURRENT_TIMESTAMP, 7, 'ORDER', '订单维度', '分析客户订单行为，包括订单频率、金额、商品偏好等', 1),
('1', 1, '系统管理员', CURRENT_TIMESTAMP, 7, 'DISPLAY', '铺货维度', '分析客户铺货情况，包括铺货率、商品覆盖度等', 2);
```

### 2. 插入数据源及数据项示例

```sql
INSERT INTO qc_ai_data_source (
    status, creator_id, creator_name, create_time, source_name, source_code, belong_dimension_code, belong_dimension_name, api_url, http_method, description
) VALUES
('1', 1, '系统管理员', CURRENT_TIMESTAMP, '分销订单', 'distributionOrder', 'ORDER', '订单', 'https://api.example.com/order', 'POST', '分销订单接口'),
('1', 1, '系统管理员', CURRENT_TIMESTAMP, '铺货上报', 'stockReport', 'DISPLAY', '铺货', 'https://api.example.com/display', 'POST', '铺货上报接口');

INSERT INTO qc_ai_data_source_ref_data_item (
    status, item_code, item_name, query_business_code, data_type_code, placeholder_name, description, sort_order
) VALUES
('1', 'distributionOrderSatisfactionRate', '订单满足率', 'distributionOrder', 'METRIC', '${distributionOrderSatisfactionRate}', '订单满足率指标', 1),
('1', 'distributionOrderDetailFields', '分销订单明细', 'distributionOrder', 'DETAIL', '${distributionOrderDetailFields}', '分销订单明细数据', 2);
```

### 3. 插入衡量标准示例

```sql
INSERT INTO qc_ai_measurement_standard (
    status, creator_id, creator_name, create_time, standard_name, standard_definition, standard_type_code, standard_type, sort_order
) VALUES
('1', 1, '系统管理员', CURRENT_TIMESTAMP, '健康', '总分≥6分判定为健康', 'DIMENSION', '维度标准', 1),
('1', 1, '系统管理员', CURRENT_TIMESTAMP, '良好', '总分≥4分且<6分判定为良好', 'DIMENSION', '维度标准', 2);
```

### 4. 插入总结配置示例

```sql
INSERT INTO qc_ai_summary_config (
    status, creator_id, creator_name, create_time, agent_id, comprehensive_prompt, summary_advice_prompt
) VALUES
('1', 1, '系统管理员', CURRENT_TIMESTAMP, 7, '请综合分析各维度表现，给出总体评价。', '请根据分析结果给出改进建议。');
```

### 5. 插入对话扩展与数据源记录示例

```sql
INSERT INTO qc_ai_conversation_ext (
    status, conversation_id, insight_object_id, insight_object_name, diagnosis_conclusion, order_insight_evaluation, order_insight_result, display_evaluation, display_insight_result, summary_advice
) VALUES
('1', 1001, 2001, '张三门店', '整体表现良好', '订单满足率达标', '{"orderRate":0.95}', '铺货率有提升空间', '{"displayRate":0.85}', '建议加强铺货管理');

INSERT INTO qc_ai_insight_data_source_record (
    status, create_time, customer_id, customer_name, dimension_ref_data_item_rel_id, value
) VALUES
('1', CURRENT_TIMESTAMP, 2001, '张三门店', 1, '{"orderRate":0.95}');
```

---

## 五、数据库结构变更说明

### 5.1 与原始设计的差异

1. **qc_ai_data_source 表结构优化**：

   - 移除了`query_business`、`query_business_code`、`data_type_code`、`data_type`字段
   - 新增了`belong_dimension_code`、`belong_dimension_name`、`query_field_name`、`query_time_start_field`、`query_time_end_field`字段
   - 这些变更使得数据源与维度的关联更加直接和清晰

2. **qc_ai_dimension_ref_data_item_rel 表简化**：

   - 移除了`query_range_field`、`query_range_value`、`query_range_enums`字段
   - 新增了`query_value`字段，简化了查询参数配置
   - 新增了`sort_order`字段，支持数据项排序

3. **qc_ai_measurement_standard 表增强**：

   - 新增了`sort_order`字段，支持标准排序

4. **qc_ai_data_source_ref_data_item 表优化**：
   - 新增了`sort_order`字段，支持数据项排序
   - 保持了`item_code`的唯一约束

### 5.2 设计优势

1. **更清晰的维度关联**：数据源直接关联到具体维度，减少了中间表的复杂性
2. **简化的查询配置**：使用单一的`query_value`字段替代复杂的查询范围配置
3. **更好的排序支持**：为关键表添加了排序字段，提升用户体验
4. **保持向后兼容**：核心业务逻辑保持不变，只是优化了数据结构

---

## 六、附注

- 所有主键字段已为 BIGSERIAL/BIGINT，适合大数据量场景。
- 建议为高频查询字段（如 customer_id, create_time 等）建立索引。
- `qc_ai_insight_data_source_record`表建议按`create_time`进行分区管理。
- 所有表均建议补充必要的唯一约束和外键约束以保证数据一致性。
- 数据源编码和数据项编码具有业务含义，便于理解和维护。

**文档版本**：v2.0.0  
**最后更新**：2025-01-29  
**更新说明**：基于实际数据库结构进行了全面更新，确保文档与数据库结构完全一致
