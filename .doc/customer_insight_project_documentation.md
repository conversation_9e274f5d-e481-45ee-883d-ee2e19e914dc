# 客户洞察助手项目文档

## 项目概述

客户洞察助手是一个基于 AI 的智能分析系统，旨在帮助企业和经销商深入分析客户行为、订单模式、铺货情况等多个维度，并提供个性化的改进建议。

### 核心功能

- **多维度分析**：订单维度、铺货维度、生动化维度等
- **智能总结**：基于各维度分析结果生成综合评估和建议
- **灵活配置**：支持自定义分析维度和衡量标准
- **实时洞察**：通过 AI 模型生成个性化的客户洞察报告

### 已完成功能

✅ **工作区配置管理**：完整的维度配置和总结配置 CRUD 操作  
✅ **数据源管理**：支持多种数据源和数据项的灵活配置  
✅ **标准管理**：维度标准和总结标准的统一管理  
✅ **API 接口**：完整的 RESTful API 接口，支持前端交互  
✅ **业务逻辑**：完整的业务逻辑实现，包括事务管理

---

## 数据库设计

### 核心表结构

#### 1. 维度配置表 (`qc_ai_dimension_config`)

```sql
-- 定义智能体的各种分析维度及其配置
CREATE TABLE qc_ai_dimension_config (
    id BIGSERIAL PRIMARY KEY,
    agent_id BIGINT NOT NULL,                    -- 关联的智能体ID
    dimension_code VARCHAR(20),                  -- 维度编码（ORDER、DISPLAY等）
    dimension_name VARCHAR(100),                 -- 维度展示名称
    interpretation_prompt TEXT,                  -- 大模型解读提示词
    sort_order INTEGER,                          -- 排序顺序
    -- 审计字段...
);
```

#### 2. 总结配置表 (`qc_ai_summary_config`)

```sql
-- 定义综合分析和建议生成规则
CREATE TABLE qc_ai_summary_config (
    id BIGSERIAL PRIMARY KEY,
    agent_id BIGINT NOT NULL,                    -- 关联的智能体ID
    comprehensive_prompt TEXT,                   -- 综合衡量标准提示词
    summary_advice_prompt TEXT,                  -- 总结及建议提示词
    -- 审计字段...
);
```

#### 3. 衡量标准表 (`qc_ai_measurement_standard`)

```sql
-- 定义各维度的衡量标准
CREATE TABLE qc_ai_measurement_standard (
    id BIGSERIAL PRIMARY KEY,
    standard_name VARCHAR(100),                  -- 标准名称
    standard_definition TEXT,                    -- 标准定义
    standard_type_code VARCHAR(10),              -- 标准类型（DIMENSION/SUMMARY）
    standard_type VARCHAR(20),                   -- 标准类型中文名称
    sort_order INTEGER,                          -- 排序顺序
    -- 审计字段...
);
```

#### 4. 配置标准关联表 (`qc_ai_config_standard_rel`)

```sql
-- 配置与衡量标准的多对多关联
CREATE TABLE qc_ai_config_standard_rel (
    id BIGSERIAL PRIMARY KEY,
    config_id BIGINT NOT NULL,                   -- 配置ID（维度或总结配置）
    config_type VARCHAR(10),                     -- 配置类型（DIMENSION/SUMMARY）
    standard_id BIGINT NOT NULL,                 -- 标准ID
    -- 审计字段...
);
```

#### 5. 数据源表 (`qc_ai_data_source`)

```sql
-- 定义可用的数据源接口
CREATE TABLE qc_ai_data_source (
    id BIGSERIAL PRIMARY KEY,
    source_name VARCHAR(100),                    -- 数据源名称
    source_code VARCHAR(50),                     -- 数据源编码
    belong_dimension_code VARCHAR(50),           -- 从属维度编码
    belong_dimension_name VARCHAR(100),          -- 从属维度名称
    api_url VARCHAR(255),                        -- 接口URL
    http_method VARCHAR(10),                     -- 请求方式
    -- 审计字段...
);
```

#### 6. 数据项定义表 (`qc_ai_data_source_ref_data_item`)

```sql
-- 定义数据源引用的数据项
CREATE TABLE qc_ai_data_source_ref_data_item (
    id BIGSERIAL PRIMARY KEY,
    item_code VARCHAR(50),                       -- 数据项编码
    item_name VARCHAR(100),                      -- 数据项显示名称
    query_business_code VARCHAR(50),             -- 关联的查询业务
    placeholder_name VARCHAR(50),                -- 在提示词中的占位符
    sort_order INTEGER,                          -- 排序顺序
    -- 审计字段...
);
```

#### 7. 维度数据项关系表 (`qc_ai_dimension_ref_data_item_rel`)

```sql
-- 维度配置与数据项的关联关系
CREATE TABLE qc_ai_dimension_ref_data_item_rel (
    id BIGSERIAL PRIMARY KEY,
    dimension_config_id BIGINT NOT NULL,         -- 维度配置ID
    ref_data_item_id BIGINT NOT NULL,            -- 引用的数据项ID
    query_value VARCHAR(50),                     -- 查询参数值
    sort_order INTEGER,                          -- 排序顺序
    -- 审计字段...
);
```

---

## 代码结构

### 1. 控制器层 (Controller)

#### 文件位置

```
admin/src/main/java/com/qc/agent/app/agent/controller/CustomerInsightAssistantController.java
```

#### 主要接口

- `POST /ai-agent/assistant/customer-insight/intent` - 客户洞察意图识别
- `GET /ai-agent/assistant/customer-insight/{agentId}` - 获取工作区配置
- `POST /ai-agent/assistant/customer-insight/saveWorkspace` - 保存工作区配置

#### 业务说明

- 提供 RESTful API 接口
- 处理 HTTP 请求和响应
- 调用服务层处理业务逻辑

### 2. 服务层 (Service)

#### 接口文件

```
admin/src/main/java/com/qc/agent/app/agent/service/CustomerInsightService.java
```

#### 实现文件

```
admin/src/main/java/com/qc/agent/app/agent/service/impl/CustomerInsightServiceImpl.java
```

#### 核心方法

- `intent()` - 意图识别和业务处理
- `getWorkspace()` - 获取完整的工作区配置
- `saveWorkspace()` - 全量保存工作区配置

#### 业务说明

- 实现核心业务逻辑
- 处理维度配置和总结配置的 CRUD 操作
- 管理数据源、数据项、标准的关联关系
- 支持事务管理和异常处理

### 3. 数据访问层 (Mapper)

#### 维度配置相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightDimensionConfigMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_dimension_config_mapping.xml
```

#### 总结配置相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightSummaryConfigMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_summary_config_mapping.xml
```

#### 标准相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightStandardMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_standard_mapping.xml
```

#### 数据源相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightDataSourceMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_data_source_mapping.xml
```

#### 数据项相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightDataItemMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_data_item_mapping.xml
```

#### 关联关系相关

```
admin/src/main/java/com/qc/agent/app/agent/mapper/InsightConfigStandardRelMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_config_standard_rel_mapping.xml

admin/src/main/java/com/qc/agent/app/agent/mapper/InsightDimensionRefItemRelMapper.java
admin/src/main/resources/com/qc/agent/app/agent/insight_dimension_ref_item_rel_mapping.xml
```

### 4. 模型层 (Model)

#### 实体类 (Entity)

```
admin/src/main/java/com/qc/agent/app/agent/model/entity/
├── InsightDimensionConfig.java      # 维度配置实体
├── InsightSummaryConfig.java        # 总结配置实体
├── InsightStandard.java             # 衡量标准实体
├── InsightDataSource.java           # 数据源实体
├── InsightDataItem.java             # 数据项实体
├── InsightConfigStandardRel.java    # 配置标准关联实体
└── InsightDimensionRefItemRel.java  # 维度数据项关联实体
```

#### 数据传输对象 (DTO)

```
admin/src/main/java/com/qc/agent/app/agent/model/dto/
├── InsightConfigWorkspaceDTO.java   # 工作区配置DTO
├── InsightDimensionConfigDTO.java   # 维度配置DTO
├── InsightSummaryConfigDTO.java     # 总结配置DTO
├── InsightStandardDTO.java          # 标准DTO
└── InsightDataSourceDTO.java        # 数据源DTO
```

#### 视图对象 (VO)

```
admin/src/main/java/com/qc/agent/app/agent/model/vo/
├── InsightConfigWorkspaceVO.java    # 工作区配置VO
├── InsightDimensionConfigVO.java    # 维度配置VO
├── InsightSummaryConfigVO.java      # 总结配置VO
├── InsightDataSourceVO.java         # 数据源VO
└── InsightDataItemGroup.java        # 数据项分组VO
```

#### 查询对象 (Query)

```
admin/src/main/java/com/qc/agent/app/agent/model/query/
└── QcAiCustomerInsightAssistantQuery.java  # 客户洞察查询参数
```

### 5. 工具类 (Utils)

```
admin/src/main/java/com/qc/agent/app/agent/util/
├── InsightDataItemTreeUtils.java    # 数据项树形结构工具
└── [其他工具类]
```

---

## 业务逻辑说明

### 1. 工作区配置管理

#### 获取工作区配置 (`getWorkspace`)

1. **查询维度配置**：获取指定 Agent 的所有维度配置
2. **构建数据源结构**：按数据源分组，构建树形数据项结构
3. **标记选中状态**：标记当前配置中选中的数据项
4. **查询总结配置**：获取总结配置及其关联的标准
5. **构建可用选项**：提供所有可用的配置选项

#### 保存工作区配置 (`saveWorkspace`)

1. **维度配置处理**：

   - 删除不存在的配置
   - 新增或更新现有配置
   - 管理数据项关联关系
   - 处理标准关联

2. **总结配置处理**：
   - 判断是否为删除操作（字段为空）
   - 新增或更新总结配置
   - 管理标准关联

### 2. 维度配置逻辑

#### 维度编码自动推导

- **ORDER** → 订单维度
- **DISPLAY** → 铺货维度
- **VIVID** → 生动化维度
- **PAID_DISPLAY** → 付费陈列维度
- **COUPON** → 兑换券维度
- **ASSET** → 资产维度
- **AUTHENTICITY** → 真实性维度
- **CUSTOM** → 自定义维度

#### 数据项选择逻辑

- 支持树形结构的数据项选择
- 自动处理父子关系
- 避免重复选择

### 3. 总结配置逻辑

#### 配置唯一性

- 每个 Agent 只能有一个总结配置
- 通过 agentId 自动判断新增/更新/删除

#### 删除操作

- 通过空字段而不是 null 对象控制删除
- 支持部分字段清空

### 4. 标准管理逻辑

#### 标准类型

- **DIMENSION**：维度标准
- **SUMMARY**：总结标准

#### 标准操作

- **新增**：没有 ID 的标准
- **修改**：有 ID 的标准
- **删除**：不在数组中的标准

---

## 接口文档

### 1. 获取工作区配置

#### 请求

```
GET /ai-agent/assistant/customer-insight/{agentId}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "currentDimensionConfiguration": [
      {
        "id": 1,
        "dimensionCode": "ORDER",
        "dimensionName": "订单维度",
        "interpretationPrompt": "分析客户订单行为...",
        "sortOrder": 1,
        "dataSources": [
          {
            "id": 1,
            "sourceName": "订单数据源",
            "sourceCode": "distributionOrder",
            "queryValue": "1",
            "selectedDataItems": [...]
          }
        ],
        "standards": [...]
      }
    ],
    "currentSummaryConfiguration": {
      "agentId": 7,
      "comprehensivePrompt": "基于各维度分析结果...",
      "summaryAdvicePrompt": "根据综合分析结果...",
      "standards": [...]
    },
    "availableOptions": {
      "dimensions": [...]
    }
  }
}
```

### 2. 保存工作区配置

#### 请求

```
POST /ai-agent/assistant/customer-insight/saveWorkspace
Content-Type: application/json
```

#### 请求体

```json
{
  "agentId": 7,
  "dimensionConfigurations": [
    {
      "interpretationPrompt": "分析客户订单行为",
      "dataSources": [
        {
          "queryValue": "1",
          "selectedDataItemIds": [1, 2, 3]
        }
      ],
      "standards": [
        {
          "standardName": "订单活跃度标准",
          "standardDefinition": "客户在最近1个月内下单次数≥3次为活跃客户"
        }
      ]
    }
  ],
  "summaryConfiguration": {
    "comprehensivePrompt": "基于各维度分析结果，综合评估客户整体表现",
    "summaryAdvicePrompt": "根据综合分析结果，为客户提供具体的改进建议",
    "standards": [
      {
        "standardName": "客户综合评分标准",
        "standardDefinition": "综合各维度表现，客户综合评分≥80分为优秀客户"
      }
    ]
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 3. 客户洞察意图识别

#### 请求

```
POST /ai-agent/assistant/customer-insight/intent
Content-Type: application/json
```

#### 请求体

```json
{
  "agentId": 7,
  "message": "请分析客户张三的订单情况",
  "conversationId": "conv_123"
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "intent": "CUSTOMER_INSIGHT",
    "answer": "根据分析结果...",
    "conversationId": "conv_123"
  }
}
```

---

## 测试用例

### 1. 新增维度配置

```json
{
  "agentId": 7,
  "dimensionConfigurations": [
    {
      "interpretationPrompt": "分析客户订单行为，包括订单频率、金额、商品偏好等",
      "dataSources": [
        {
          "queryValue": "1",
          "selectedDataItemIds": [1, 2, 3]
        }
      ],
      "standards": [
        {
          "standardName": "订单活跃度标准",
          "standardDefinition": "客户在最近1个月内下单次数≥3次为活跃客户"
        }
      ]
    }
  ],
  "summaryConfiguration": null
}
```

### 2. 修改维度配置

```json
{
  "agentId": 7,
  "dimensionConfigurations": [
    {
      "id": 1,
      "interpretationPrompt": "深度分析客户订单行为模式，识别购买偏好和消费习惯",
      "dataSources": [
        {
          "queryValue": "3",
          "selectedDataItemIds": [1, 2, 6]
        }
      ],
      "standards": [
        {
          "id": 1,
          "standardName": "订单活跃度标准（已修改）",
          "standardDefinition": "客户在最近3个月内下单次数≥5次为活跃客户"
        },
        {
          "standardName": "新增标准",
          "standardDefinition": "客户复购率≥30%为忠诚客户"
        }
      ]
    }
  ],
  "summaryConfiguration": null
}
```

### 3. 新增总结配置

```json
{
  "agentId": 7,
  "dimensionConfigurations": null,
  "summaryConfiguration": {
    "comprehensivePrompt": "基于各维度分析结果，综合评估客户整体表现，识别关键优势和潜在风险",
    "summaryAdvicePrompt": "根据综合分析结果，为客户提供具体的改进建议和行动方案",
    "standards": [
      {
        "standardName": "客户综合评分标准",
        "standardDefinition": "综合各维度表现，客户综合评分≥80分为优秀客户"
      }
    ]
  }
}
```

### 4. 删除总结配置

```json
{
  "agentId": 7,
  "dimensionConfigurations": null,
  "summaryConfiguration": {
    "comprehensivePrompt": "",
    "summaryAdvicePrompt": "",
    "standards": []
  }
}
```

---

## 开发规范

### 1. 命名规范

- **类名**：使用 PascalCase，如`InsightDimensionConfig`
- **方法名**：使用 camelCase，如`getWorkspace`
- **字段名**：使用 camelCase，如`comprehensivePrompt`
- **常量**：使用 UPPER_SNAKE_CASE，如`CONFIG_TYPE_DIMENSION`

### 2. 代码分层

- **Controller**：处理 HTTP 请求，参数校验
- **Service**：业务逻辑处理，事务管理
- **Mapper**：数据访问，SQL 执行
- **Model**：数据结构定义

### 3. 异常处理

- 使用统一的异常处理机制
- 提供清晰的错误信息
- 记录详细的错误日志

### 4. 事务管理

- 在 Service 层使用`@Transactional`注解
- 确保数据一致性
- 合理设置事务传播行为

---

## 部署说明

### 1. 环境要求

- **JDK**：1.8+
- **数据库**：PostgreSQL 12+
- **应用服务器**：Spring Boot 2.x

### 2. 配置文件

```
admin/src/main/resources/
├── application.yml                    # 主配置文件
├── application-chatbot.yml           # 聊天机器人配置
├── application-elastic-job.yml       # 定时任务配置
└── application-jdbc.yml              # 数据库配置
```

### 3. 数据库初始化

```sql
-- 执行数据库初始化脚本
source .doc/customer_insight_schema.sql
source .doc/data.sql
```

### 4. 启动命令

```bash
# 开发环境
mvn spring-boot:run -pl admin

# 生产环境
java -jar admin/target/admin.jar --spring.profiles.active=prod
```

---

## 维护说明

### 1. 日志管理

- 使用 SLF4J + Logback
- 配置日志级别和输出格式
- 定期清理日志文件

### 2. 性能监控

- 监控数据库查询性能
- 监控接口响应时间
- 监控系统资源使用情况

### 3. 数据备份

- 定期备份数据库
- 备份配置文件
- 建立数据恢复机制

### 4. 版本管理

- 使用 Git 进行版本控制
- 建立分支管理策略
- 记录版本变更日志

---

## 常见问题

### 1. 配置保存失败

- 检查数据库连接
- 验证事务配置
- 查看错误日志

### 2. 数据项选择异常

- 检查数据项 ID 是否存在
- 验证父子关系是否正确
- 确认数据源配置

### 3. 标准关联失败

- 检查标准 ID 是否有效
- 验证配置类型是否正确
- 确认关联关系配置

---

## 项目状态

### ✅ 已完成功能

1. **数据库设计**：完整的表结构设计和初始化脚本
2. **核心业务逻辑**：维度配置、总结配置的完整 CRUD 操作
3. **API 接口**：完整的 RESTful API 接口实现
4. **数据访问层**：MyBatis 映射文件和 Mapper 接口
5. **业务服务层**：完整的业务逻辑实现
6. **异常处理**：统一的异常处理机制
7. **事务管理**：完整的事务支持

### 🔄 进行中功能

1. **前端集成**：与前端页面的集成测试
2. **性能优化**：数据库查询优化和缓存策略

### 📋 待开发功能

1. **AI 模型集成**：与腾讯云混元大模型的深度集成
2. **数据源接口**：实际数据源的接口对接
3. **报表生成**：洞察报告的自动生成
4. **权限管理**：用户权限和角色管理

---

## 联系方式

如有问题，请联系开发团队或查看项目文档。

**文档版本**：v2.0.0  
**最后更新**：2025-01-29  
**维护人员**：开发团队
